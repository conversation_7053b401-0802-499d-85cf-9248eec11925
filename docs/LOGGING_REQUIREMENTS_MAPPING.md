# MOVER Logging Requirements - Database Schema Mapping

## Overview

This document maps each specific logging requirement to the corresponding database fields in the `activity_logs` and `signature_history` tables.

## Database Tables Created

### 1. `activity_logs` - Main logging table
### 2. `signature_history` - Signature tracking table

---

## APP Logging Requirements Mapping

### General Logging Requirements

| Requirement | Database Field(s) | Action Type | Action Category |
|-------------|------------------|-------------|-----------------|
| Capture all activities performed within the APP | `action_description`, `action_type` | Various | Various |
| Log user login events with date and time | `login_time`, `action_type='LOGIN'` | LOGIN | AUTH |
| Track actions performed at shipment level | `shipment_job_id`, `action_description` | Various | Various |

### Stage 1: Add Items to Inventory

| Requirement | Database Field(s) | Action Type | Action Category |
|-------------|------------------|-------------|-----------------|
| Log which user accessed this stage | `staff_id/admin_id`, `stage_action='ACCESSED'` | STAGE_ACCESS | STAGE_ACCESS |
| Record number of items added by each user | `items_added_count` | ITEM_ADD | INVENTORY_MGMT |
| Capture edits made to items (fields modified) | `fields_modified`, `old_values`, `new_values` | ITEM_EDIT | INVENTORY_MGMT |
| Track item duplication events | `items_duplicated_count` | ITEM_DUPLICATE | INVENTORY_MGMT |
| Track item deletion events | `items_deleted_count` | ITEM_DELETE | INVENTORY_MGMT |

### Stage 2: Add Items to Storage

| Requirement | Database Field(s) | Action Type | Action Category |
|-------------|------------------|-------------|-----------------|
| Log which user accessed this stage | `staff_id/admin_id`, `stage_action='ACCESSED'` | STAGE_ACCESS | STAGE_ACCESS |
| Capture add items to storage vs view items selection | `stage_action='ASSIGN_TO_STORAGE_SELECTED'` or `'VIEW_ITEMS_SELECTED'` | STAGE_OPTION_SELECT | STAGE_ACCESS |
| Record manual unit selection | `selection_method='MANUAL'`, `unit_id`, `storage_unit_id` | UNIT_ASSIGN | STORAGE_MGMT |
| Record QR code unit scanning | `selection_method='QR_SCAN'`, `qr_codes_scanned` | UNIT_ASSIGN_QR | STORAGE_MGMT |

### Stage 3: Remove Items from Storage

| Requirement | Database Field(s) | Action Type | Action Category |
|-------------|------------------|-------------|-----------------|
| Log which user accessed this stage | `staff_id/admin_id`, `stage_action='ACCESSED'` | STAGE_ACCESS | STAGE_ACCESS |
| Capture remove from storage vs view items selection | `stage_action='REMOVE_FROM_STORAGE_SELECTED'` or `'VIEW_ITEMS_SELECTED'` | STAGE_OPTION_SELECT | STAGE_ACCESS |
| Track manual item selection for removal | `selection_method='MANUAL'`, `items_removed_from_storage` | ITEM_REMOVE_STORAGE | STORAGE_MGMT |
| Track QR code item scanning for removal | `selection_method='QR_SCAN'`, `qr_codes_scanned` | ITEM_REMOVE_STORAGE_QR | STORAGE_MGMT |

### Stage 4: Remove Items from Inventory

| Requirement | Database Field(s) | Action Type | Action Category |
|-------------|------------------|-------------|-----------------|
| Log which user accessed this stage | `staff_id/admin_id`, `stage_action='ACCESSED'` | STAGE_ACCESS | STAGE_ACCESS |
| Capture remove from inventory vs view items selection | `stage_action='REMOVE_FROM_INVENTORY_SELECTED'` or `'VIEW_ITEMS_SELECTED'` | STAGE_OPTION_SELECT | STAGE_ACCESS |
| Track manual item selection for removal | `selection_method='MANUAL'`, `items_removed_from_inventory` | ITEM_REMOVE_INVENTORY | INVENTORY_MGMT |
| Track QR code item scanning for removal | `selection_method='QR_SCAN'`, `qr_codes_scanned` | ITEM_REMOVE_INVENTORY_QR | INVENTORY_MGMT |

### Mover Storage APP

| Requirement | Database Field(s) | Action Type | Action Category |
|-------------|------------------|-------------|-----------------|
| Log item to unit mapping with date/time | `unit_id`, `shipment_inventory_id`, `created_at` | ITEM_UNIT_MAP | STORAGE_MGMT |
| Log unit movement from staging to racks/vaults | `unit_location_from`, `unit_location_to` | UNIT_MOVE | STORAGE_MGMT |
| Capture item removal from unit with user/date | `items_removed_from_storage`, `staff_id`, `created_at` | ITEM_UNIT_REMOVE | STORAGE_MGMT |

---

## CMS Logging Requirements Mapping

### Shipment Management

| Requirement | Database Field(s) | Action Type | Action Category |
|-------------|------------------|-------------|-----------------|
| Log shipment creation | `shipment_job_id`, `new_values` | SHIPMENT_CREATE | SHIPMENT_MGMT |
| Track stage management (add/edit/delete/deactivate) | `stage_id`, `action_type`, `old_values`, `new_values` | STAGE_ADD/EDIT/DELETE/DEACTIVATE | SHIPMENT_MGMT |
| Capture user-level activity history | `staff_id/admin_id`, `action_description`, `created_at` | Various | Various |

### Signature History (signature_history table)

| Requirement | Database Field(s) | Notes |
|-------------|------------------|-------|
| Signature history per stage | `shipment_job_id`, `stage_id`, `signature_captured_at` | Separate table for signatures |
| Items added to inventory count | `items_added_to_inventory` | Count at time of signature |
| Items added to storage count | `items_added_to_storage` | Count at time of signature |
| Items removed from storage count | `items_removed_from_storage` | Count at time of signature |
| Items removed from inventory count | `items_removed_from_inventory` | Count at time of signature |

### System Management

| Requirement | Database Field(s) | Action Type | Action Category |
|-------------|------------------|-------------|-----------------|
| Track shipment type management | `action_type`, `old_values`, `new_values` | SHIPMENT_TYPE_ADD/EDIT | SYSTEM_CONFIG |
| Log room list management | `action_type`, `old_values`, `new_values` | ROOM_ADD/EDIT/DELETE/DEACTIVATE | SYSTEM_CONFIG |
| Capture item list management | `action_type`, `old_values`, `new_values` | ITEM_LIST_ADD/EDIT/DELETE/DEACTIVATE | SYSTEM_CONFIG |
| Log tag management | `action_type`, `old_values`, `new_values` | TAG_ADD/EDIT/DELETE/DEACTIVATE | SYSTEM_CONFIG |
| Track user management | `action_type`, `old_values`, `new_values` | USER_ADD/EDIT/DELETE/DEACTIVATE | USER_MGMT |
| Capture customer management | `action_type`, `old_values`, `new_values` | CUSTOMER_ADD/EDIT/DELETE/DEACTIVATE | USER_MGMT |

---

## Access & Review Requirements

| Requirement | Implementation | Notes |
|-------------|----------------|-------|
| Viewable by user administrators in CMS | Admin API endpoints with authentication | Role-based access control |
| Filters by user, date range, action type | Query parameters in API | Indexed fields for performance |
| Secure storage with retention | Database with proper indexes | Configurable retention policies |

---

## Common Fields for All Logs

| Field | Purpose | Always Populated |
|-------|---------|------------------|
| `log_id` | Unique identifier | Yes |
| `admin_id/staff_id/customer_id` | User identification | One of these |
| `company_id` | Company context | Yes |
| `action_type` | Specific action performed | Yes |
| `action_category` | High-level categorization | Yes |
| `action_description` | Human-readable description | Yes |
| `ip_address` | Security tracking | When available |
| `user_agent` | Device/browser info | When available |
| `device_type` | Platform (ios/android/web) | When available |
| `session_id` | Session correlation | When available |
| `created_at` | Timestamp | Yes |
| `status` | Success/Failed/Warning | Yes |

---

## Action Types Reference

### Authentication
- `LOGIN` - User login
- `LOGOUT` - User logout

### Stage Access
- `STAGE_ACCESS` - User accessed a stage
- `STAGE_OPTION_SELECT` - User selected an option within a stage

### Inventory Management
- `ITEM_ADD` - Item added to inventory
- `ITEM_EDIT` - Item modified
- `ITEM_DELETE` - Item deleted
- `ITEM_DUPLICATE` - Item duplicated
- `ITEM_REMOVE_INVENTORY` - Item removed from inventory
- `ITEM_REMOVE_INVENTORY_QR` - Item removed via QR scan

### Storage Management
- `UNIT_ASSIGN` - Item assigned to storage unit
- `UNIT_ASSIGN_QR` - Unit assigned via QR scan
- `ITEM_REMOVE_STORAGE` - Item removed from storage
- `ITEM_REMOVE_STORAGE_QR` - Item removed via QR scan
- `ITEM_UNIT_MAP` - Item mapped to unit
- `UNIT_MOVE` - Unit moved between locations
- `ITEM_UNIT_REMOVE` - Item removed from unit

### System Configuration
- `SHIPMENT_CREATE` - Shipment created
- `STAGE_ADD/EDIT/DELETE/DEACTIVATE` - Stage management
- `SHIPMENT_TYPE_ADD/EDIT` - Shipment type management
- `ROOM_ADD/EDIT/DELETE/DEACTIVATE` - Room management
- `ITEM_LIST_ADD/EDIT/DELETE/DEACTIVATE` - Item list management
- `TAG_ADD/EDIT/DELETE/DEACTIVATE` - Tag management

### User Management
- `USER_ADD/EDIT/DELETE/DEACTIVATE` - User management
- `CUSTOMER_ADD/EDIT/DELETE/DEACTIVATE` - Customer management

### Signatures
- `SIGNATURE_CAPTURE` - Signature captured for stage

---

## Usage Examples

### Log Stage Access
```sql
INSERT INTO activity_logs (
  staff_id, company_id, action_type, action_category, 
  action_description, shipment_job_id, stage_id, stage_name,
  stage_action, created_at
) VALUES (
  123, 1, 'STAGE_ACCESS', 'STAGE_ACCESS',
  'User accessed Add Items to Inventory stage',
  456, 1, 'Add Items to Inventory', 'ACCESSED', NOW()
);
```

### Log Item Addition
```sql
INSERT INTO activity_logs (
  staff_id, company_id, action_type, action_category,
  action_description, shipment_job_id, stage_id,
  items_added_count, new_values, created_at
) VALUES (
  123, 1, 'ITEM_ADD', 'INVENTORY_MGMT',
  'Added 2 items to inventory',
  456, 1, 2, '{"items": ["Sofa", "Chair"]}', NOW()
);
```

### Log Signature Capture
```sql
INSERT INTO signature_history (
  shipment_job_id, stage_id, stage_name, staff_id, company_id,
  items_added_to_inventory, items_added_to_storage,
  items_removed_from_storage, items_removed_from_inventory,
  signature_captured_at
) VALUES (
  456, 1, 'Add Items to Inventory', 123, 1,
  15, 10, 5, 2, NOW()
);
```

This comprehensive mapping ensures that every requirement is tracked with appropriate database fields and can be queried efficiently for reporting and troubleshooting purposes.
