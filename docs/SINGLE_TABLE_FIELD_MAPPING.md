# MOVER Logging System - Single Table Field Mapping

## Overview
This document explains how each field in the single `activity_logs` table maps to specific logging requirements.

## Table: `activity_logs`

### USER IDENTIFICATION FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `admin_id` | INTEGER | CMS Admin user identification | **CMS Logging**: Track which admin created shipments, managed stages, edited users/customers/rooms/tags |
| `staff_id` | INTEGER | APP Staff user identification | **APP Logging**: Track which staff accessed stages, added/edited/deleted items, performed storage operations |
| `customer_id` | INTEGER | Customer user identification | **Customer Management**: Track customer portal activities and customer-related operations |
| `company_id` | INTEGER | Company context for data isolation | **All Requirements**: Filter logs by company, ensure data separation |

### ACTION CLASSIFICATION FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `action_type` | STRING(100) | Specific action identifier | **All Requirements**: LOGIN, LOGOUT, STAGE_ACCESS, ITEM_ADD, ITEM_EDIT, ITEM_DELETE, ITEM_DUPLICATE, etc. |
| `action_category` | ENUM | High-level categorization | **Filtering**: AUTH, STAGE_ACCESS, INVENTORY_MGMT, STORAGE_MGMT, SHIPMENT_MGMT, USER_MGMT, SYSTEM_CONFIG, SIGNATURE |
| `action_description` | TEXT | Human-readable description | **Search & Review**: Detailed description for admin interface search functionality |

### CONTEXT FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `shipment_job_id` | INTEGER | Shipment context | **"Track actions at shipment level"** - Links all actions to specific shipments |
| `shipment_inventory_id` | INTEGER | Specific item context | **Item Operations**: Track operations on specific inventory items |
| `stage_id` | INTEGER | Stage identifier | **Stage Access**: Track which stage was accessed (Stages 1-4) |
| `stage_name` | STRING(255) | Stage name for easy identification | **Stage Access**: "Add Items to Inventory", "Add Items to Storage", etc. |

### STAGE ACCESS TRACKING FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `stage_action` | ENUM | Stage-specific actions | **Stage 2**: "capture whether user selected add items to storage or view items"<br>**Stage 3**: "capture whether user selected remove from storage or view items"<br>**Stage 4**: "capture whether user selected remove from inventory or view items" |

**ENUM Values:**
- `ACCESSED` - User accessed the stage
- `ADD_ITEMS_SELECTED` - Stage 1: Selected add items option  
- `VIEW_ITEMS_SELECTED` - All stages: Selected view items option
- `ASSIGN_TO_STORAGE_SELECTED` - Stage 2: Selected assign to storage
- `REMOVE_FROM_STORAGE_SELECTED` - Stage 3: Selected remove from storage
- `REMOVE_FROM_INVENTORY_SELECTED` - Stage 4: Selected remove from inventory

### ITEM OPERATIONS COUNTING FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `items_added_count` | INTEGER | Count of items added | **Stage 1**: "Record the number of items added to inventory by each user"<br>**Signature History**: "items added to inventory" |
| `items_edited_count` | INTEGER | Count of items edited | **Stage 1**: "Capture any edits made to items" |
| `items_deleted_count` | INTEGER | Count of items deleted | **Stage 1**: "Track item deletion events" |
| `items_duplicated_count` | INTEGER | Count of items duplicated | **Stage 1**: "Track item duplication events" |
| `items_assigned_to_storage` | INTEGER | Count assigned to storage | **Stage 2**: Storage assignment operations<br>**Signature History**: "items added to storage" |
| `items_removed_from_storage` | INTEGER | Count removed from storage | **Stage 3**: Storage removal operations<br>**Signature History**: "items removed from storage" |
| `items_removed_from_inventory` | INTEGER | Count removed from inventory | **Stage 4**: Inventory removal operations<br>**Signature History**: "items removed from inventory" |

### SELECTION METHOD TRACKING FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `selection_method` | ENUM | How items/units were selected | **Stage 2**: "record whether user selected unit manually or scanned QR code"<br>**Stage 3**: "track whether they selected item manually or scanned QR code"<br>**Stage 4**: "track whether they selected item manually or scanned QR code" |
| `qr_codes_scanned` | JSON | Array of QR codes scanned | **QR Tracking**: Store specific QR codes used in operations |

**ENUM Values:**
- `MANUAL` - Manual selection
- `QR_SCAN` - QR code scanning
- `MIXED` - Combination of both methods

### DATA MODIFICATION TRACKING FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `fields_modified` | JSON | List of modified field names | **Stage 1**: "fields modified such as description, notes, volume/weight" |
| `old_values` | JSON | Previous values before change | **CMS Operations**: Track changes in user management, system configuration |
| `new_values` | JSON | New values after change | **CMS Operations**: Track new values in edits and additions |

### STORAGE UNIT TRACKING FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `unit_id` | INTEGER | Unit identifier | **Storage APP**: "item mapped to a unit", unit assignment operations |
| `storage_unit_id` | STRING(150) | Storage unit code/name | **Storage Operations**: Track specific storage units |
| `unit_location_from` | STRING(255) | Previous location | **Storage APP**: "unit moved from staging area to racks/vaults" |
| `unit_location_to` | STRING(255) | New location | **Storage APP**: "unit moved from staging area to racks/vaults" |

### TECHNICAL TRACKING FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `ip_address` | STRING(45) | User IP address | **Security & Troubleshooting**: Track user locations |
| `user_agent` | TEXT | Browser/device information | **Device Tracking**: Identify browsers and devices |
| `device_type` | ENUM | Platform type | **Platform Tracking**: ios, android, web |
| `session_id` | STRING(255) | Session identifier | **Session Correlation**: Group related actions |

### LOGIN/SESSION TRACKING FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `login_time` | DATE | Login timestamp | **"Log user login events with date and time"** |
| `logout_time` | DATE | Logout timestamp | **Logout Tracking**: Track when users log out |
| `session_duration_minutes` | INTEGER | Session length | **Session Analysis**: Calculate how long users were active |

### SIGNATURE TRACKING FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `signature_data` | TEXT | Signature information | **Signature Capture**: Store signature data or file path |
| `signature_items_added_to_inventory` | INTEGER | Items count at signature time | **Signature History**: "items added to inventory" |
| `signature_items_added_to_storage` | INTEGER | Items count at signature time | **Signature History**: "items added to storage" |
| `signature_items_removed_from_storage` | INTEGER | Items count at signature time | **Signature History**: "items removed from storage" |
| `signature_items_removed_from_inventory` | INTEGER | Items count at signature time | **Signature History**: "items removed from inventory" |

### SYSTEM FIELDS

| Field | Type | Used For | Requirement Mapping |
|-------|------|----------|-------------------|
| `additional_data` | JSON | Extra context information | **Flexible Storage**: Any additional data not covered by other fields |
| `status` | ENUM | Operation status | **Error Tracking**: SUCCESS, FAILED, WARNING |
| `error_message` | TEXT | Error details | **Troubleshooting**: Store error information for failed operations |
| `created_at` | DATE | Record creation time | **All Date Requirements**: "with date and time" for all operations |
| `updated_at` | DATE | Record update time | **Audit Trail**: Track when records are modified |

## Usage Examples by Requirement

### Stage 1: Add Items to Inventory
```sql
-- Log stage access
INSERT INTO activity_logs (staff_id, company_id, action_type, action_category, 
    action_description, shipment_job_id, stage_id, stage_name, stage_action)
VALUES (123, 1, 'STAGE_ACCESS', 'STAGE_ACCESS', 
    'User accessed Add Items to Inventory stage', 456, 1, 'Add Items to Inventory', 'ACCESSED');

-- Log item addition
INSERT INTO activity_logs (staff_id, company_id, action_type, action_category,
    action_description, shipment_job_id, stage_id, items_added_count, new_values)
VALUES (123, 1, 'ITEM_ADD', 'INVENTORY_MGMT',
    'Added 2 items to inventory', 456, 1, 2, '{"items": ["Sofa", "Chair"]}');

-- Log item edit
INSERT INTO activity_logs (staff_id, company_id, action_type, action_category,
    action_description, shipment_job_id, shipment_inventory_id, items_edited_count,
    fields_modified, old_values, new_values)
VALUES (123, 1, 'ITEM_EDIT', 'INVENTORY_MGMT',
    'Edited item description and weight', 456, 789, 1,
    '["description", "weight"]', '{"description": "Old desc", "weight": 10}', 
    '{"description": "New desc", "weight": 15}');
```

### Stage 2: Add Items to Storage
```sql
-- Log stage access and option selection
INSERT INTO activity_logs (staff_id, company_id, action_type, action_category,
    action_description, shipment_job_id, stage_id, stage_name, stage_action)
VALUES (123, 1, 'STAGE_OPTION_SELECT', 'STAGE_ACCESS',
    'Selected assign to storage option', 456, 2, 'Add Items to Storage', 'ASSIGN_TO_STORAGE_SELECTED');

-- Log unit assignment via QR scan
INSERT INTO activity_logs (staff_id, company_id, action_type, action_category,
    action_description, shipment_job_id, stage_id, items_assigned_to_storage,
    selection_method, qr_codes_scanned, unit_id, storage_unit_id)
VALUES (123, 1, 'UNIT_ASSIGN', 'STORAGE_MGMT',
    'Assigned items to storage unit via QR scan', 456, 2, 3,
    'QR_SCAN', '["QR123", "QR456"]', 789, 'UNIT-A-001');
```

### CMS Logging
```sql
-- Log shipment creation
INSERT INTO activity_logs (admin_id, company_id, action_type, action_category,
    action_description, shipment_job_id, new_values)
VALUES (456, 1, 'SHIPMENT_CREATE', 'SHIPMENT_MGMT',
    'Created new shipment for customer ABC', 789, 
    '{"customer_id": 123, "shipment_name": "ABC Move", "pickup_date": "2025-01-15"}');

-- Log user management
INSERT INTO activity_logs (admin_id, company_id, action_type, action_category,
    action_description, old_values, new_values)
VALUES (456, 1, 'USER_EDIT', 'USER_MGMT',
    'Updated staff user permissions', 
    '{"role": "basic", "permissions": ["read"]}',
    '{"role": "advanced", "permissions": ["read", "write"]}');
```

### Signature History
```sql
-- Log signature capture with item counts
INSERT INTO activity_logs (staff_id, company_id, action_type, action_category,
    action_description, shipment_job_id, stage_id, stage_name,
    signature_data, signature_items_added_to_inventory, signature_items_added_to_storage,
    signature_items_removed_from_storage, signature_items_removed_from_inventory)
VALUES (123, 1, 'SIGNATURE_CAPTURE', 'SIGNATURE',
    'Captured signature for stage completion', 456, 1, 'Add Items to Inventory',
    'base64_signature_data_here', 15, 10, 5, 2);
```

This single table design captures all logging requirements while maintaining simplicity and query performance through proper indexing.
