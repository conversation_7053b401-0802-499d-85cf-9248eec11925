# MOVER Inventory API - Comprehensive Logging System

## Overview

This document describes the comprehensive logging system implemented for the MOVER Inventory API to track all user activities, system events, and API calls for better troubleshooting and audit trails.

## Features

### ✅ Implemented Features

1. **Single Comprehensive Log Table** - All logs stored in `system_logs` table
2. **Automatic API Logging** - All API requests/responses logged automatically
3. **User Authentication Tracking** - Login/logout events with session management
4. **Inventory Operations Logging** - Add, edit, delete, duplicate item operations
5. **Stage Access Tracking** - When users access different workflow stages
6. **Admin Interface** - Web-based log viewer with filtering and search
7. **Detailed Context Tracking** - IP addresses, user agents, session IDs
8. **Error Tracking** - Failed operations with error messages
9. **Performance Metrics** - Request duration tracking

## Database Schema

### system_logs Table

```sql
CREATE TABLE system_logs (
  log_id INT PRIMARY KEY AUTO_INCREMENT,
  
  -- User identification
  admin_id INT,
  staff_id INT, 
  customer_id INT,
  company_id INT,
  
  -- Action details
  action_type VARCHAR(100) NOT NULL,
  action_category ENUM('AUTH', 'INVENTORY', 'STORAGE', 'SHIPMENT', 'ADMIN', 'SYSTEM'),
  action_description TEXT NOT NULL,
  
  -- Context
  shipment_job_id INT,
  shipment_inventory_id INT,
  stage_id INT,
  stage_name VARCHAR(255),
  
  -- Technical details
  ip_address VARCHAR(45),
  user_agent TEXT,
  device_type ENUM('ios', 'android', 'web'),
  session_id VARCHAR(255),
  api_endpoint VARCHAR(500),
  http_method VARCHAR(10),
  
  -- Data tracking
  old_values JSON,
  new_values JSON,
  items_count INT DEFAULT 0,
  selection_method ENUM('MANUAL', 'QR_SCAN', 'BULK'),
  qr_codes_scanned JSON,
  fields_modified JSON,
  additional_data JSON,
  
  -- Status
  status ENUM('SUCCESS', 'FAILED', 'WARNING') DEFAULT 'SUCCESS',
  error_message TEXT,
  duration_ms INT,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Usage Examples

### 1. Basic Logging in Controllers

```javascript
const { loggingService } = require("../middlewares/loggingMiddleware");

// Log a simple action
await loggingService.log({
  req,
  action_type: 'ITEM_ADD',
  action_category: 'INVENTORY',
  action_description: 'Added new inventory item',
  shipment_job_id: 123,
  items_count: 1
});
```

### 2. Using Middleware for Route Logging

```javascript
// In routes file
const { stageAccessMiddleware, inventoryOperationMiddleware } = require("../middlewares/loggingMiddleware");

router
  .route("/add-item")
  .post(
    authentication.validateToken,
    stageAccessMiddleware('Add Items to Inventory'),
    inventoryOperationMiddleware('ADD'),
    homeController.addItem
  );
```

### 3. Logging in Controllers with Details

```javascript
// In controller after successful operation
await logInventoryOperationAfter(request, {
  shipment_inventory_id: itemDetail.shipment_inventory_id,
  shipment_job_id: request.body.job_id,
  stage_id: request.body.stageId,
  stage_name: 'Add Items to Inventory',
  items_count: 1,
  new_values: {
    item_name: request.body.item_name,
    description: request.body.description,
    weight: request.body.weight
  },
  additional_data: {
    photos_uploaded: request.files ? Object.keys(request.files).length : 0
  }
});
```

## API Endpoints

### Admin Log Viewing APIs

1. **GET /api/admin/logs** - Get logs with filtering
   - Query parameters: `page`, `limit`, `action_type`, `action_category`, `user_type`, `date_from`, `date_to`, `search`, `status`

2. **GET /api/admin/logs/stats** - Get log statistics
   - Returns category stats, action type stats, daily activity, error counts

3. **GET /api/admin/logs/:log_id** - Get specific log details

4. **GET /api/admin/logs/viewer** - Web interface for viewing logs

## Logged Activities

### APP Logging (Mobile/Web App)

#### Authentication
- ✅ User login events with date/time
- ✅ User logout events with session duration
- ✅ Failed login attempts with reasons

#### Stage 1: Add Items to Inventory
- ✅ User accessing this stage
- ✅ Number of items added by each user
- ✅ Item edits with modified fields
- ✅ Item duplication events
- ✅ Item deletion events

#### Stage 2: Add Items to Storage
- ✅ User accessing this stage
- ✅ Selection method tracking (manual vs QR scan)
- ✅ Unit assignment operations

#### Stage 3: Remove Items from Storage
- ✅ User accessing this stage
- ✅ Item removal operations
- ✅ Selection method tracking

#### Stage 4: Remove Items from Inventory
- ✅ User accessing this stage
- ✅ Item removal from inventory
- ✅ Selection method tracking

#### Storage APP
- ✅ Item to unit mapping with date/time
- ✅ Unit movement from staging to racks/vaults
- ✅ Item removal from units

### CMS Logging (Admin Interface)

#### Shipment Management
- ✅ Shipment creation
- ✅ Stage management (add, edit, delete, deactivate)
- ✅ User activity history
- ✅ Signature history per stage

#### System Management
- ✅ Shipment type management
- ✅ Room list management
- ✅ Item list management
- ✅ Tag management
- ✅ User management
- ✅ Customer management

## Log Categories

1. **AUTH** - Authentication events (login, logout)
2. **INVENTORY** - Item operations (add, edit, delete, duplicate)
3. **STORAGE** - Storage operations (assign to storage, remove from storage)
4. **SHIPMENT** - Shipment and job management
5. **ADMIN** - CMS administrative operations
6. **SYSTEM** - System events and API calls

## Access Control

- **Master Admin Credentials** - Full access to all logs across all companies
- **Company Admins** - Access to logs for their company only
- **Staff Users** - No direct log access (their activities are logged)

## Performance Considerations

1. **Indexes** - Comprehensive indexing on frequently queried fields
2. **Pagination** - All log queries support pagination
3. **Async Logging** - Logging operations don't block main application flow
4. **Error Handling** - Logging failures don't affect main application functionality

## Maintenance

### Log Retention
- Logs are retained indefinitely by default
- Implement archiving strategy based on business requirements
- Consider partitioning by date for large datasets

### Monitoring
- Monitor log table size growth
- Set up alerts for high error rates
- Regular backup of log data

## Security

1. **Sensitive Data** - Passwords and tokens are never logged
2. **Access Control** - Log viewing requires admin authentication
3. **IP Tracking** - All actions include IP address for security auditing
4. **Session Tracking** - Session IDs help correlate related activities

## Future Enhancements

1. **Real-time Notifications** - Alert on critical errors
2. **Advanced Analytics** - User behavior analysis
3. **Export Functionality** - CSV/Excel export of filtered logs
4. **Log Aggregation** - Summary reports and dashboards
5. **Integration** - Connect with external monitoring tools

## Troubleshooting

### Common Issues

1. **Missing Logs** - Check if middleware is properly configured
2. **Performance Issues** - Verify database indexes are in place
3. **Storage Growth** - Implement log rotation/archiving

### Debug Mode
Enable detailed logging by setting environment variable:
```bash
LOG_LEVEL=debug
```

## Testing

Run the logging system tests:
```bash
npm test -- --grep "logging"
```

Test specific scenarios:
1. User authentication flows
2. Inventory operations
3. Error conditions
4. API endpoint logging

---

For technical support or questions about the logging system, contact the development team.
