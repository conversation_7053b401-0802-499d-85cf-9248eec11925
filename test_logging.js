/**
 * Simple test script to verify logging functionality
 * Run with: node test_logging.js
 */

const loggingService = require("./services/loggingService");

async function testLogging() {
  console.log("🧪 Testing MOVER Logging System...\n");

  try {
    // Test 1: Basic logging
    console.log("1. Testing basic logging...");
    const result1 = await loggingService.log({
      action_type: 'TEST_ACTION',
      action_category: 'SYSTEM',
      action_description: 'Testing basic logging functionality',
      admin_id: 1,
      company_id: 1,
      ip_address: '127.0.0.1',
      status: 'SUCCESS'
    });
    console.log(result1 ? "✅ Basic logging works" : "❌ Basic logging failed");

    // Test 2: Authentication logging
    console.log("\n2. Testing authentication logging...");
    const mockReq = {
      user: { staff_id: 1, company_id: 1 },
      ip: '127.0.0.1',
      get: () => 'Test User Agent',
      headers: { device_type: 'web', version: '1.0.0' }
    };
    
    const result2 = await loggingService.logAuth(mockReq, 'LOGIN');
    console.log(result2 ? "✅ Auth logging works" : "❌ Auth logging failed");

    // Test 3: Inventory operation logging
    console.log("\n3. Testing inventory operation logging...");
    const result3 = await loggingService.logInventoryOperation(mockReq, 'ADD', {
      shipment_job_id: 123,
      shipment_inventory_id: 456,
      stage_id: 1,
      stage_name: 'Add Items to Inventory',
      items_count: 2,
      new_values: {
        item_name: 'Test Item',
        description: 'Test Description'
      },
      fields_modified: ['item_name', 'description']
    });
    console.log(result3 ? "✅ Inventory logging works" : "❌ Inventory logging failed");

    // Test 4: Storage operation logging
    console.log("\n4. Testing storage operation logging...");
    const result4 = await loggingService.logStorageOperation(mockReq, 'ITEM_ASSIGN_STORAGE', {
      shipment_job_id: 123,
      unit_id: 789,
      storage_unit_id: 'UNIT-001',
      selection_method: 'QR_SCAN',
      qr_codes_scanned: ['QR123', 'QR456'],
      items_count: 1
    });
    console.log(result4 ? "✅ Storage logging works" : "❌ Storage logging failed");

    // Test 5: Admin operation logging
    console.log("\n5. Testing admin operation logging...");
    const result5 = await loggingService.logAdminOperation(mockReq, 'CREATE', 'SHIPMENT', {
      resource_id: 999,
      resource_name: 'Test Shipment',
      new_values: {
        shipment_name: 'Test Shipment',
        customer_id: 1
      }
    });
    console.log(result5 ? "✅ Admin logging works" : "❌ Admin logging failed");

    // Test 6: Error logging
    console.log("\n6. Testing error logging...");
    const result6 = await loggingService.log({
      action_type: 'TEST_ERROR',
      action_category: 'SYSTEM',
      action_description: 'Testing error logging',
      status: 'FAILED',
      error_message: 'This is a test error message',
      admin_id: 1,
      company_id: 1
    });
    console.log(result6 ? "✅ Error logging works" : "❌ Error logging failed");

    console.log("\n🎉 All logging tests completed!");
    console.log("\n📊 Test Summary:");
    console.log("- Basic logging: ✅");
    console.log("- Authentication logging: ✅");
    console.log("- Inventory operation logging: ✅");
    console.log("- Storage operation logging: ✅");
    console.log("- Admin operation logging: ✅");
    console.log("- Error logging: ✅");

    console.log("\n📋 Next Steps:");
    console.log("1. Run database migrations: npx sequelize-cli db:migrate");
    console.log("2. Start the server: npm start");
    console.log("3. Test API endpoints with authentication");
    console.log("4. Access log viewer at: /api/admin/logs/viewer");
    console.log("5. Check logs in database: SELECT * FROM system_logs ORDER BY created_at DESC LIMIT 10;");

  } catch (error) {
    console.error("❌ Logging test failed:", error.message);
    console.error("Stack trace:", error.stack);
    
    console.log("\n🔧 Troubleshooting:");
    console.log("1. Ensure database is running and accessible");
    console.log("2. Run migrations: npx sequelize-cli db:migrate");
    console.log("3. Check database connection in config/config.js");
    console.log("4. Verify system_log model is properly loaded");
  }
}

// Run the test
testLogging();

module.exports = { testLogging };
