const { system_log } = require("../database/schemas");
const crypto = require("crypto");

class LoggingService {
  constructor() {
    this.sessionStore = new Map(); // Store session start times
  }

  /**
   * Generate a unique session ID
   */
  generateSessionId() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Extract user information from request
   */
  extractUserInfo(req) {
    return {
      admin_id: req.user?.admin_id || null,
      staff_id: req.user?.staff_id || null,
      customer_id: req.user?.customer_id || null,
      company_id: req.company_id || req.user?.company_id || null,
      ip_address: req.ip || req.connection?.remoteAddress || req.headers['x-forwarded-for'] || null,
      user_agent: req.get('User-Agent') || null,
      device_type: req.headers.device_type || 'web',
      session_id: req.session?.id || req.headers.session_id || req.session_id || null,
    };
  }

  /**
   * Main logging method - handles all types of logs
   */
  async log({
    // User context (can be passed directly or extracted from req)
    req = null,
    admin_id = null,
    staff_id = null,
    customer_id = null,
    company_id = null,

    // Action details (required)
    action_type,
    action_category,
    action_description,

    // Context (optional)
    shipment_job_id = null,
    shipment_inventory_id = null,
    stage_id = null,
    stage_name = null,

    // Technical details (optional)
    ip_address = null,
    user_agent = null,
    device_type = null,
    session_id = null,
    api_endpoint = null,
    http_method = null,

    // Data tracking (optional)
    old_values = null,
    new_values = null,
    items_count = 0,
    selection_method = null,
    qr_codes_scanned = null,
    fields_modified = null,
    additional_data = null,

    // Status (optional)
    status = 'SUCCESS',
    error_message = null,
    duration_ms = null
  }) {
    try {
      let userInfo = {};

      // Extract user info from request if provided
      if (req) {
        userInfo = this.extractUserInfo(req);
        api_endpoint = api_endpoint || req.originalUrl || req.url;
        http_method = http_method || req.method;
      }

      const logData = {
        // User identification
        admin_id: admin_id || userInfo.admin_id,
        staff_id: staff_id || userInfo.staff_id,
        customer_id: customer_id || userInfo.customer_id,
        company_id: company_id || userInfo.company_id,

        // Action details
        action_type,
        action_category,
        action_description,

        // Context
        shipment_job_id,
        shipment_inventory_id,
        stage_id,
        stage_name,

        // Technical details
        ip_address: ip_address || userInfo.ip_address,
        user_agent: user_agent || userInfo.user_agent,
        device_type: device_type || userInfo.device_type,
        session_id: session_id || userInfo.session_id,
        api_endpoint,
        http_method,

        // Data tracking
        old_values,
        new_values,
        items_count,
        selection_method,
        qr_codes_scanned,
        fields_modified,
        additional_data,

        // Status
        status,
        error_message,
        duration_ms,

        // Timestamps
        created_at: new Date(),
        updated_at: new Date(),
      };

      await system_log.create(logData);
      return true;
    } catch (error) {
      console.error('Error creating system log:', error);
      return false;
    }
  }

  /**
   * Log user login/logout
   */
  async logAuth(req, action_type, additional_info = {}) {
    const session_id = req.session_id || this.generateSessionId();

    if (action_type === 'LOGIN') {
      this.sessionStore.set(session_id, Date.now());
      req.session_id = session_id; // Store for future use
    }

    let duration_ms = null;
    if (action_type === 'LOGOUT' && this.sessionStore.has(session_id)) {
      duration_ms = Date.now() - this.sessionStore.get(session_id);
      this.sessionStore.delete(session_id);
    }

    return await this.log({
      req,
      action_type,
      action_category: 'AUTH',
      action_description: `User ${action_type.toLowerCase()}`,
      session_id,
      duration_ms,
      additional_data: {
        device_token: req.headers.device_token,
        app_version: req.headers.version,
        ...additional_info
      }
    });
  }

  /**
   * Log stage access
   */
  async logStageAccess(req, stage_name, stage_id = null, shipment_job_id = null) {
    return await this.log({
      req,
      action_type: 'STAGE_ACCESS',
      action_category: 'INVENTORY',
      action_description: `Accessed ${stage_name} stage`,
      stage_id,
      stage_name,
      shipment_job_id
    });
  }

  /**
   * Log inventory operations
   */
  async logInventoryOperation(req, operation_type, details = {}) {
    const actionMap = {
      'ADD': 'ITEM_ADD',
      'EDIT': 'ITEM_EDIT',
      'DELETE': 'ITEM_DELETE',
      'DUPLICATE': 'ITEM_DUPLICATE'
    };

    return await this.log({
      req,
      action_type: actionMap[operation_type] || operation_type,
      action_category: 'INVENTORY',
      action_description: `${operation_type} ${details.items_count || 1} item(s)${details.fields_modified ? ` - Modified: ${details.fields_modified.join(', ')}` : ''}`,
      shipment_job_id: details.shipment_job_id,
      shipment_inventory_id: details.shipment_inventory_id,
      stage_id: details.stage_id,
      stage_name: details.stage_name,
      old_values: details.old_values,
      new_values: details.new_values,
      items_count: details.items_count || 1,
      fields_modified: details.fields_modified,
      additional_data: details.additional_data
    });
  }

  /**
   * Log storage operations
   */
  async logStorageOperation(req, operation_type, details = {}) {
    return await this.log({
      req,
      action_type: operation_type,
      action_category: 'STORAGE',
      action_description: `${operation_type} - ${details.items_count || 1} item(s) ${details.selection_method ? `via ${details.selection_method}` : ''}`,
      shipment_job_id: details.shipment_job_id,
      shipment_inventory_id: details.shipment_inventory_id,
      stage_id: details.stage_id,
      stage_name: details.stage_name,
      items_count: details.items_count || 1,
      selection_method: details.selection_method,
      qr_codes_scanned: details.qr_codes_scanned,
      additional_data: details.additional_data
    });
  }

  /**
   * Log CMS/Admin operations
   */
  async logAdminOperation(req, operation_type, resource_type, details = {}) {
    return await this.log({
      req,
      action_type: `${resource_type.toUpperCase()}_${operation_type.toUpperCase()}`,
      action_category: 'ADMIN',
      action_description: `${operation_type} ${resource_type}${details.resource_name ? ` - ${details.resource_name}` : ''}`,
      old_values: details.old_values,
      new_values: details.new_values,
      additional_data: {
        resource_id: details.resource_id,
        ...details.additional_data
      }
    });
  }

  /**
   * Log API calls (for middleware)
   */
  async logApiCall(req, res, responseData = null, duration_ms = null) {
    const statusCode = res.statusCode;
    const status = statusCode >= 200 && statusCode < 400 ? 'SUCCESS' : 'FAILED';

    // Determine category based on endpoint
    let action_category = 'SYSTEM';
    if (req.originalUrl.includes('/api/home') || req.originalUrl.includes('/api/staff')) {
      action_category = 'INVENTORY';
    } else if (req.originalUrl.includes('/api/admin')) {
      action_category = 'ADMIN';
    }

    return await this.log({
      req,
      action_type: 'API_CALL',
      action_category,
      action_description: `${req.method} ${req.originalUrl} - ${statusCode}`,
      status,
      error_message: status === 'FAILED' ? `HTTP ${statusCode}` : null,
      duration_ms,
      additional_data: {
        status_code: statusCode,
        request_body: req.body,
        response_data: responseData,
        query_params: req.query
      }
    });
  }
}

module.exports = new LoggingService();
