const { activity_log, user_session_log, stage_activity_log } = require("../database/schemas");
const moment = require("moment");
const crypto = require("crypto");

class LoggingService {
  constructor() {
    this.sessionStore = new Map(); // Store active sessions
  }

  /**
   * Generate a unique session ID
   */
  generateSessionId() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Extract user information from request
   */
  extractUserInfo(req) {
    const userInfo = {
      admin_id: req.user?.admin_id || null,
      staff_id: req.user?.staff_id || null,
      customer_id: req.user?.customer_id || null,
      company_id: req.company_id || req.user?.company_id || null,
      ip_address: req.ip || req.connection?.remoteAddress || null,
      user_agent: req.get('User-Agent') || null,
      device_type: req.headers.device_type || 'web',
      session_id: req.session?.id || req.headers.session_id || null,
    };
    return userInfo;
  }

  /**
   * Log general activity
   */
  async logActivity({
    req = null,
    admin_id = null,
    staff_id = null,
    customer_id = null,
    company_id = null,
    activity_type,
    activity_category,
    activity_description,
    shipment_job_id = null,
    shipment_inventory_id = null,
    stage_id = null,
    api_endpoint = null,
    http_method = null,
    old_values = null,
    new_values = null,
    additional_data = null,
    status = 'SUCCESS',
    error_message = null,
    session_id = null,
    ip_address = null,
    user_agent = null,
    device_type = null
  }) {
    try {
      let userInfo = {};
      
      if (req) {
        userInfo = this.extractUserInfo(req);
        api_endpoint = api_endpoint || req.originalUrl || req.url;
        http_method = http_method || req.method;
      }

      const logData = {
        admin_id: admin_id || userInfo.admin_id,
        staff_id: staff_id || userInfo.staff_id,
        customer_id: customer_id || userInfo.customer_id,
        company_id: company_id || userInfo.company_id,
        activity_type,
        activity_category,
        activity_description,
        shipment_job_id,
        shipment_inventory_id,
        stage_id,
        ip_address: ip_address || userInfo.ip_address,
        user_agent: user_agent || userInfo.user_agent,
        device_type: device_type || userInfo.device_type,
        api_endpoint,
        http_method,
        old_values: old_values ? JSON.stringify(old_values) : null,
        new_values: new_values ? JSON.stringify(new_values) : null,
        additional_data: additional_data ? JSON.stringify(additional_data) : null,
        status,
        error_message,
        session_id: session_id || userInfo.session_id,
        created_at: new Date(),
        updated_at: new Date(),
      };

      await activity_log.create(logData);
      return true;
    } catch (error) {
      console.error('Error logging activity:', error);
      return false;
    }
  }

  /**
   * Log user login/logout activities
   */
  async logUserSession({
    req = null,
    admin_id = null,
    staff_id = null,
    customer_id = null,
    company_id = null,
    session_id,
    access_token = null,
    action_type,
    login_time = null,
    logout_time = null,
    login_status = 'SUCCESS',
    failure_reason = null,
    device_token = null,
    app_version = null,
    location_info = null,
    ip_address = null,
    user_agent = null,
    device_type = null
  }) {
    try {
      let userInfo = {};
      
      if (req) {
        userInfo = this.extractUserInfo(req);
      }

      const sessionData = {
        admin_id: admin_id || userInfo.admin_id,
        staff_id: staff_id || userInfo.staff_id,
        customer_id: customer_id || userInfo.customer_id,
        company_id: company_id || userInfo.company_id,
        session_id,
        access_token,
        action_type,
        login_time: login_time || (action_type === 'LOGIN' ? new Date() : null),
        logout_time: logout_time || (action_type === 'LOGOUT' ? new Date() : null),
        ip_address: ip_address || userInfo.ip_address,
        user_agent: user_agent || userInfo.user_agent,
        device_type: device_type || userInfo.device_type,
        device_token,
        app_version: app_version || req?.headers?.version,
        login_status,
        failure_reason,
        location_info: location_info ? JSON.stringify(location_info) : null,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Calculate session duration for logout
      if (action_type === 'LOGOUT' && this.sessionStore.has(session_id)) {
        const loginTime = this.sessionStore.get(session_id);
        sessionData.session_duration = Math.floor((new Date() - loginTime) / 1000);
        this.sessionStore.delete(session_id);
      } else if (action_type === 'LOGIN') {
        this.sessionStore.set(session_id, new Date());
      }

      await user_session_log.create(sessionData);
      return true;
    } catch (error) {
      console.error('Error logging user session:', error);
      return false;
    }
  }

  /**
   * Log stage-specific activities
   */
  async logStageActivity({
    req = null,
    admin_id = null,
    staff_id = null,
    company_id = null,
    shipment_job_id,
    stage_id,
    stage_name = null,
    stage_action,
    activity_details = null,
    items_added_count = 0,
    items_edited_count = 0,
    items_deleted_count = 0,
    items_assigned_to_storage = 0,
    items_removed_from_storage = 0,
    items_removed_from_inventory = 0,
    selection_method = null,
    qr_codes_scanned = null,
    session_id = null,
    duration_seconds = null,
    additional_data = null
  }) {
    try {
      let userInfo = {};
      
      if (req) {
        userInfo = this.extractUserInfo(req);
      }

      const stageLogData = {
        admin_id: admin_id || userInfo.admin_id,
        staff_id: staff_id || userInfo.staff_id,
        company_id: company_id || userInfo.company_id,
        shipment_job_id,
        stage_id,
        stage_name,
        stage_action,
        activity_details,
        items_added_count,
        items_edited_count,
        items_deleted_count,
        items_assigned_to_storage,
        items_removed_from_storage,
        items_removed_from_inventory,
        selection_method,
        qr_codes_scanned: qr_codes_scanned ? JSON.stringify(qr_codes_scanned) : null,
        session_id: session_id || userInfo.session_id,
        duration_seconds,
        additional_data: additional_data ? JSON.stringify(additional_data) : null,
        created_at: new Date(),
        updated_at: new Date(),
      };

      await stage_activity_log.create(stageLogData);
      return true;
    } catch (error) {
      console.error('Error logging stage activity:', error);
      return false;
    }
  }

  /**
   * Log inventory item operations
   */
  async logInventoryOperation({
    req,
    operation_type, // 'ADD', 'EDIT', 'DELETE', 'DUPLICATE'
    shipment_job_id,
    shipment_inventory_id = null,
    stage_id = null,
    item_count = 1,
    old_item_data = null,
    new_item_data = null,
    fields_modified = null,
    additional_context = null
  }) {
    const activityTypeMap = {
      'ADD': 'ITEM_ADD',
      'EDIT': 'ITEM_EDIT',
      'DELETE': 'ITEM_DELETE',
      'DUPLICATE': 'ITEM_DUPLICATE'
    };

    const description = `${operation_type} operation on ${item_count} item(s)${fields_modified ? ` - Modified fields: ${fields_modified.join(', ')}` : ''}`;

    return await this.logActivity({
      req,
      activity_type: activityTypeMap[operation_type],
      activity_category: 'APP_INVENTORY',
      activity_description: description,
      shipment_job_id,
      shipment_inventory_id,
      stage_id,
      old_values: old_item_data,
      new_values: new_item_data,
      additional_data: {
        item_count,
        fields_modified,
        ...additional_context
      }
    });
  }

  /**
   * Log storage operations
   */
  async logStorageOperation({
    req,
    operation_type, // 'ASSIGN_TO_STORAGE', 'REMOVE_FROM_STORAGE', 'UNIT_MAPPING', 'UNIT_MOVE'
    shipment_job_id,
    shipment_inventory_id = null,
    unit_id = null,
    storage_unit_id = null,
    stage_id = null,
    selection_method = null, // 'MANUAL', 'QR_SCAN'
    qr_codes_scanned = null,
    item_count = 1,
    additional_context = null
  }) {
    const description = `${operation_type} operation - ${item_count} item(s) ${selection_method ? `via ${selection_method}` : ''}`;

    return await this.logActivity({
      req,
      activity_type: operation_type,
      activity_category: 'APP_STORAGE',
      activity_description: description,
      shipment_job_id,
      shipment_inventory_id,
      stage_id,
      additional_data: {
        unit_id,
        storage_unit_id,
        selection_method,
        qr_codes_scanned,
        item_count,
        ...additional_context
      }
    });
  }
}

module.exports = new LoggingService();
