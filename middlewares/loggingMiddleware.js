const loggingService = require("../services/loggingService");
const { v4: uuidv4 } = require('uuid');

/**
 * Middleware to automatically log API requests and responses
 */
const apiLoggingMiddleware = (req, res, next) => {
  // Generate session ID if not present
  if (!req.session?.id && !req.headers.session_id) {
    req.session_id = loggingService.generateSessionId();
  }

  // Store request start time
  req.startTime = Date.now();

  // Capture original response methods
  const originalSend = res.send;
  const originalJson = res.json;

  // Override response methods to capture response data
  res.send = function(data) {
    logApiCall(req, res, data);
    return originalSend.call(this, data);
  };

  res.json = function(data) {
    logApiCall(req, res, data);
    return originalJson.call(this, data);
  };

  next();
};

/**
 * Log API call details
 */
async function logApiCall(req, res, responseData) {
  try {
    const duration = Date.now() - req.startTime;
    const statusCode = res.statusCode;
    
    // Determine if this was successful or failed
    const status = statusCode >= 200 && statusCode < 400 ? 'SUCCESS' : 'FAILED';
    
    // Parse response data if it's a string
    let parsedResponse = responseData;
    if (typeof responseData === 'string') {
      try {
        parsedResponse = JSON.parse(responseData);
      } catch (e) {
        // Keep as string if not valid JSON
      }
    }

    // Determine activity category based on endpoint
    let activity_category = 'API_EXTERNAL';
    if (req.originalUrl.includes('/api/home') || req.originalUrl.includes('/api/staff')) {
      activity_category = 'APP_INVENTORY';
    } else if (req.originalUrl.includes('/api/admin')) {
      activity_category = 'CMS_ADMIN';
    }

    // Log the API call
    await loggingService.logActivity({
      req,
      activity_type: 'API_CALL',
      activity_category,
      activity_description: `${req.method} ${req.originalUrl} - ${statusCode} (${duration}ms)`,
      api_endpoint: req.originalUrl,
      http_method: req.method,
      additional_data: {
        request_body: req.body,
        response_data: parsedResponse,
        status_code: statusCode,
        duration_ms: duration,
        query_params: req.query,
        headers: {
          'user-agent': req.get('User-Agent'),
          'content-type': req.get('Content-Type'),
          'authorization': req.get('Authorization') ? '[REDACTED]' : null
        }
      },
      status,
      error_message: status === 'FAILED' ? `HTTP ${statusCode}` : null
    });
  } catch (error) {
    console.error('Error in API logging middleware:', error);
  }
}

/**
 * Middleware to log user authentication events
 */
const authLoggingMiddleware = (action_type) => {
  return async (req, res, next) => {
    try {
      const userInfo = loggingService.extractUserInfo(req);
      
      // Generate session ID for login
      let session_id = req.session?.id || req.headers.session_id;
      if (action_type === 'LOGIN' && !session_id) {
        session_id = loggingService.generateSessionId();
        req.session_id = session_id;
      }

      // Log the authentication event
      await loggingService.logUserSession({
        req,
        session_id,
        action_type,
        access_token: req.headers.access_token || req.headers.authorization,
        device_token: req.headers.device_token,
        app_version: req.headers.version,
        login_status: 'SUCCESS' // Will be updated if authentication fails
      });

      // Also log as general activity
      await loggingService.logActivity({
        req,
        activity_type: action_type,
        activity_category: 'SYSTEM',
        activity_description: `User ${action_type.toLowerCase()} event`,
        session_id
      });

    } catch (error) {
      console.error('Error in auth logging middleware:', error);
    }
    
    next();
  };
};

/**
 * Middleware to log stage access
 */
const stageAccessMiddleware = (stage_name) => {
  return async (req, res, next) => {
    try {
      const shipment_job_id = req.body.shipment_job_id || req.params.shipmentId || req.query.shipment_job_id;
      const stage_id = req.body.stage_id || req.params.stageId || req.query.stage_id;

      if (shipment_job_id) {
        await loggingService.logStageActivity({
          req,
          shipment_job_id,
          stage_id,
          stage_name,
          stage_action: 'STAGE_ACCESS',
          activity_details: 'STAGE_ACCESSED'
        });

        await loggingService.logActivity({
          req,
          activity_type: 'STAGE_ACCESS',
          activity_category: 'APP_INVENTORY',
          activity_description: `Accessed ${stage_name} stage`,
          shipment_job_id,
          stage_id
        });
      }
    } catch (error) {
      console.error('Error in stage access middleware:', error);
    }
    
    next();
  };
};

/**
 * Middleware to log inventory operations
 */
const inventoryOperationMiddleware = (operation_type) => {
  return async (req, res, next) => {
    // Store operation type for later use in controller
    req.inventory_operation = operation_type;
    req.operation_start_time = Date.now();
    
    next();
  };
};

/**
 * Helper function to log inventory operation after controller execution
 */
const logInventoryOperationAfter = async (req, operation_details = {}) => {
  try {
    if (req.inventory_operation) {
      const duration = Date.now() - req.operation_start_time;
      
      await loggingService.logInventoryOperation({
        req,
        operation_type: req.inventory_operation,
        shipment_job_id: req.body.shipment_job_id || req.params.shipmentId,
        shipment_inventory_id: req.body.shipment_inventory_id || operation_details.shipment_inventory_id,
        stage_id: req.body.stage_id || operation_details.stage_id,
        item_count: operation_details.item_count || 1,
        old_item_data: operation_details.old_item_data,
        new_item_data: operation_details.new_item_data,
        fields_modified: operation_details.fields_modified,
        additional_context: {
          duration_ms: duration,
          ...operation_details.additional_context
        }
      });
    }
  } catch (error) {
    console.error('Error logging inventory operation:', error);
  }
};

/**
 * Helper function to log storage operation after controller execution
 */
const logStorageOperationAfter = async (req, operation_details = {}) => {
  try {
    const duration = Date.now() - req.operation_start_time;
    
    await loggingService.logStorageOperation({
      req,
      operation_type: operation_details.operation_type,
      shipment_job_id: req.body.shipment_job_id || req.params.shipmentId,
      shipment_inventory_id: operation_details.shipment_inventory_id,
      unit_id: operation_details.unit_id,
      storage_unit_id: operation_details.storage_unit_id,
      stage_id: operation_details.stage_id,
      selection_method: operation_details.selection_method,
      qr_codes_scanned: operation_details.qr_codes_scanned,
      item_count: operation_details.item_count || 1,
      additional_context: {
        duration_ms: duration,
        ...operation_details.additional_context
      }
    });
  } catch (error) {
    console.error('Error logging storage operation:', error);
  }
};

/**
 * Middleware to log CMS operations
 */
const cmsOperationMiddleware = (operation_type, resource_type) => {
  return async (req, res, next) => {
    try {
      const description = `${operation_type} ${resource_type}`;
      
      await loggingService.logActivity({
        req,
        activity_type: `${resource_type.toUpperCase()}_MANAGE`,
        activity_category: 'CMS_ADMIN',
        activity_description: description,
        old_values: req.method === 'PUT' || req.method === 'PATCH' ? req.original_data : null,
        new_values: req.body,
        additional_data: {
          operation_type,
          resource_type,
          resource_id: req.params.id || req.body.id
        }
      });
    } catch (error) {
      console.error('Error in CMS operation middleware:', error);
    }
    
    next();
  };
};

module.exports = {
  apiLoggingMiddleware,
  authLoggingMiddleware,
  stageAccessMiddleware,
  inventoryOperationMiddleware,
  logInventoryOperationAfter,
  logStorageOperationAfter,
  cmsOperationMiddleware,
  loggingService
};
