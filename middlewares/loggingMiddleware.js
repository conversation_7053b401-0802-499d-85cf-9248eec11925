const loggingService = require("../services/loggingService");

/**
 * Middleware to automatically log API requests and responses
 */
const apiLoggingMiddleware = (req, res, next) => {
  // Generate session ID if not present
  if (!req.session?.id && !req.headers.session_id && !req.session_id) {
    req.session_id = loggingService.generateSessionId();
  }

  // Store request start time
  req.startTime = Date.now();

  // Capture original response methods
  const originalSend = res.send;
  const originalJson = res.json;

  // Override response methods to capture response data
  res.send = function(data) {
    logApiCall(req, res, data);
    return originalSend.call(this, data);
  };

  res.json = function(data) {
    logApiCall(req, res, data);
    return originalJson.call(this, data);
  };

  next();
};

/**
 * Log API call details
 */
async function logApiCall(req, res, responseData) {
  try {
    const duration = Date.now() - req.startTime;
    
    // Only log if not already logged to avoid duplicates
    if (!req.logged) {
      await loggingService.logApiCall(req, res, responseData, duration);
      req.logged = true;
    }
  } catch (error) {
    console.error('Error in API logging middleware:', error);
  }
}

/**
 * Middleware to log user authentication events
 */
const authLoggingMiddleware = (action_type) => {
  return async (req, res, next) => {
    try {
      // Generate session ID for login
      if (action_type === 'LOGIN' && !req.session_id) {
        req.session_id = loggingService.generateSessionId();
      }

      // Log the authentication event
      await loggingService.logAuth(req, action_type);

    } catch (error) {
      console.error('Error in auth logging middleware:', error);
    }
    
    next();
  };
};

/**
 * Middleware to log stage access
 */
const stageAccessMiddleware = (stage_name) => {
  return async (req, res, next) => {
    try {
      const shipment_job_id = req.body.shipment_job_id || req.params.shipmentId || req.query.shipment_job_id;
      const stage_id = req.body.stage_id || req.params.stageId || req.query.stage_id;

      if (shipment_job_id) {
        await loggingService.logStageAccess(req, stage_name, stage_id, shipment_job_id);
      }
    } catch (error) {
      console.error('Error in stage access middleware:', error);
    }
    
    next();
  };
};

/**
 * Middleware to prepare for inventory operation logging
 */
const inventoryOperationMiddleware = (operation_type) => {
  return (req, res, next) => {
    // Store operation type for later use in controller
    req.inventory_operation = operation_type;
    req.operation_start_time = Date.now();
    
    next();
  };
};

/**
 * Helper function to log inventory operation after controller execution
 */
const logInventoryOperationAfter = async (req, operation_details = {}) => {
  try {
    if (req.inventory_operation) {
      const duration = Date.now() - req.operation_start_time;
      
      await loggingService.logInventoryOperation(req, req.inventory_operation, {
        shipment_job_id: req.body.shipment_job_id || req.params.shipmentId,
        shipment_inventory_id: req.body.shipment_inventory_id || operation_details.shipment_inventory_id,
        stage_id: req.body.stage_id || operation_details.stage_id,
        stage_name: operation_details.stage_name,
        items_count: operation_details.items_count || 1,
        old_values: operation_details.old_values,
        new_values: operation_details.new_values,
        fields_modified: operation_details.fields_modified,
        additional_data: {
          duration_ms: duration,
          ...operation_details.additional_data
        }
      });
    }
  } catch (error) {
    console.error('Error logging inventory operation:', error);
  }
};

/**
 * Helper function to log storage operation after controller execution
 */
const logStorageOperationAfter = async (req, operation_details = {}) => {
  try {
    const duration = Date.now() - req.operation_start_time;
    
    await loggingService.logStorageOperation(req, operation_details.operation_type, {
      shipment_job_id: req.body.shipment_job_id || req.params.shipmentId,
      shipment_inventory_id: operation_details.shipment_inventory_id,
      unit_id: operation_details.unit_id,
      storage_unit_id: operation_details.storage_unit_id,
      stage_id: operation_details.stage_id,
      stage_name: operation_details.stage_name,
      selection_method: operation_details.selection_method,
      qr_codes_scanned: operation_details.qr_codes_scanned,
      items_count: operation_details.items_count || 1,
      additional_data: {
        duration_ms: duration,
        ...operation_details.additional_data
      }
    });
  } catch (error) {
    console.error('Error logging storage operation:', error);
  }
};

/**
 * Middleware to log CMS operations
 */
const cmsOperationMiddleware = (operation_type, resource_type) => {
  return async (req, res, next) => {
    try {
      await loggingService.logAdminOperation(req, operation_type, resource_type, {
        resource_id: req.params.id || req.body.id,
        resource_name: req.body.name || req.body.title,
        old_values: req.original_data, // Should be set by controller if needed
        new_values: req.body
      });
    } catch (error) {
      console.error('Error in CMS operation middleware:', error);
    }
    
    next();
  };
};

/**
 * Helper function to log any custom action
 */
const logCustomAction = async (req, action_type, action_category, description, additional_data = {}) => {
  try {
    await loggingService.log({
      req,
      action_type,
      action_category,
      action_description: description,
      ...additional_data
    });
  } catch (error) {
    console.error('Error logging custom action:', error);
  }
};

module.exports = {
  apiLoggingMiddleware,
  authLoggingMiddleware,
  stageAccessMiddleware,
  inventoryOperationMiddleware,
  logInventoryOperationAfter,
  logStorageOperationAfter,
  cmsOperationMiddleware,
  logCustomAction,
  loggingService
};
