<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MOVER System Logs</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        .filter-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .filter-group select, .filter-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .stat-card .number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .logs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .logs-table th, .logs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .logs-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        .logs-table tr:hover {
            background-color: #f5f5f5;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
        .category-auth {
            background: #e3f2fd;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .category-inventory {
            background: #e8f5e8;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .category-storage {
            background: #fff3e0;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .category-admin {
            background: #fce4ec;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .category-system {
            background: #f3e5f5;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .pagination button:hover {
            background: #f8f9fa;
        }
        .pagination button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .log-detail {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MOVER System Activity Logs</h1>
            <p>Monitor and analyze all system activities, user actions, and API calls</p>
        </div>

        <!-- Statistics Section -->
        <div class="stats" id="stats">
            <div class="stat-card">
                <h3>Total Logs</h3>
                <div class="number" id="total-logs">-</div>
            </div>
            <div class="stat-card">
                <h3>Today's Activity</h3>
                <div class="number" id="today-logs">-</div>
            </div>
            <div class="stat-card">
                <h3>Failed Actions</h3>
                <div class="number" id="failed-logs">-</div>
            </div>
            <div class="stat-card">
                <h3>Active Users</h3>
                <div class="number" id="active-users">-</div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters">
            <div class="filter-group">
                <label for="action-category">Category</label>
                <select id="action-category">
                    <option value="">All Categories</option>
                    <option value="AUTH">Authentication</option>
                    <option value="INVENTORY">Inventory</option>
                    <option value="STORAGE">Storage</option>
                    <option value="ADMIN">Admin</option>
                    <option value="SYSTEM">System</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="action-type">Action Type</label>
                <select id="action-type">
                    <option value="">All Actions</option>
                    <option value="LOGIN">Login</option>
                    <option value="LOGOUT">Logout</option>
                    <option value="ITEM_ADD">Add Item</option>
                    <option value="ITEM_EDIT">Edit Item</option>
                    <option value="ITEM_DELETE">Delete Item</option>
                    <option value="STAGE_ACCESS">Stage Access</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="status">Status</label>
                <select id="status">
                    <option value="">All Status</option>
                    <option value="SUCCESS">Success</option>
                    <option value="FAILED">Failed</option>
                    <option value="WARNING">Warning</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="date-from">Date From</label>
                <input type="date" id="date-from">
            </div>
            <div class="filter-group">
                <label for="date-to">Date To</label>
                <input type="date" id="date-to">
            </div>
            <div class="filter-group">
                <label for="search">Search</label>
                <input type="text" id="search" placeholder="Search in descriptions...">
            </div>
            <div class="filter-group" style="align-self: end;">
                <button class="btn" onclick="loadLogs()">Apply Filters</button>
            </div>
            <div class="filter-group" style="align-self: end;">
                <button class="btn btn-secondary" onclick="clearFilters()">Clear</button>
            </div>
        </div>

        <!-- Logs Table -->
        <div id="logs-container">
            <div class="loading">Loading logs...</div>
        </div>

        <!-- Pagination -->
        <div class="pagination" id="pagination" style="display: none;">
            <!-- Pagination buttons will be inserted here -->
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;

        // Load logs on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadLogs();
        });

        async function loadStats() {
            try {
                // This would make an API call to get statistics
                // For demo purposes, showing static data
                document.getElementById('total-logs').textContent = '1,234';
                document.getElementById('today-logs').textContent = '56';
                document.getElementById('failed-logs').textContent = '3';
                document.getElementById('active-users').textContent = '12';
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        async function loadLogs(page = 1) {
            const container = document.getElementById('logs-container');
            container.innerHTML = '<div class="loading">Loading logs...</div>';

            try {
                // Get filter values
                const filters = {
                    page: page,
                    action_category: document.getElementById('action-category').value,
                    action_type: document.getElementById('action-type').value,
                    status: document.getElementById('status').value,
                    date_from: document.getElementById('date-from').value,
                    date_to: document.getElementById('date-to').value,
                    search: document.getElementById('search').value
                };

                // Remove empty filters
                Object.keys(filters).forEach(key => {
                    if (!filters[key]) delete filters[key];
                });

                // This would make an API call to /api/admin/logs
                // For demo purposes, showing sample data
                const sampleLogs = generateSampleLogs();
                displayLogs(sampleLogs);
                
                currentPage = page;
                totalPages = 5; // Sample
                updatePagination();

            } catch (error) {
                container.innerHTML = '<div class="error">Error loading logs: ' + error.message + '</div>';
            }
        }

        function generateSampleLogs() {
            return [
                {
                    log_id: 1,
                    action_type: 'LOGIN',
                    action_category: 'AUTH',
                    action_description: 'User login',
                    status: 'SUCCESS',
                    created_at: new Date().toISOString(),
                    staff_user: { name: 'John Doe', email: '<EMAIL>' },
                    ip_address: '*************'
                },
                {
                    log_id: 2,
                    action_type: 'ITEM_ADD',
                    action_category: 'INVENTORY',
                    action_description: 'Added 1 item(s) - Sofa, Chair',
                    status: 'SUCCESS',
                    created_at: new Date(Date.now() - 300000).toISOString(),
                    staff_user: { name: 'Jane Smith', email: '<EMAIL>' },
                    ip_address: '*************'
                },
                {
                    log_id: 3,
                    action_type: 'API_CALL',
                    action_category: 'SYSTEM',
                    action_description: 'POST /api/home/<USER>',
                    status: 'SUCCESS',
                    created_at: new Date(Date.now() - 600000).toISOString(),
                    staff_user: { name: 'Bob Wilson', email: '<EMAIL>' },
                    ip_address: '*************'
                }
            ];
        }

        function displayLogs(logs) {
            const container = document.getElementById('logs-container');
            
            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="loading">No logs found</div>';
                return;
            }

            let html = `
                <table class="logs-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>User</th>
                            <th>Action</th>
                            <th>Category</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>IP Address</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            logs.forEach(log => {
                const time = new Date(log.created_at).toLocaleString();
                const user = log.staff_user?.name || log.admin_user?.name || log.customer_user?.name || 'System';
                const statusClass = `status-${log.status.toLowerCase()}`;
                const categoryClass = `category-${log.action_category.toLowerCase()}`;

                html += `
                    <tr onclick="toggleLogDetail(${log.log_id})">
                        <td>${time}</td>
                        <td>${user}</td>
                        <td>${log.action_type}</td>
                        <td><span class="${categoryClass}">${log.action_category}</span></td>
                        <td>${log.action_description}</td>
                        <td><span class="${statusClass}">${log.status}</span></td>
                        <td>${log.ip_address || '-'}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function toggleLogDetail(logId) {
            // This would show detailed log information
            console.log('Show details for log:', logId);
        }

        function updatePagination() {
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }

            pagination.style.display = 'flex';
            
            let html = '';
            
            // Previous button
            if (currentPage > 1) {
                html += `<button onclick="loadLogs(${currentPage - 1})">Previous</button>`;
            }

            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const activeClass = i === currentPage ? 'active' : '';
                html += `<button class="${activeClass}" onclick="loadLogs(${i})">${i}</button>`;
            }

            // Next button
            if (currentPage < totalPages) {
                html += `<button onclick="loadLogs(${currentPage + 1})">Next</button>`;
            }

            pagination.innerHTML = html;
        }

        function clearFilters() {
            document.getElementById('action-category').value = '';
            document.getElementById('action-type').value = '';
            document.getElementById('status').value = '';
            document.getElementById('date-from').value = '';
            document.getElementById('date-to').value = '';
            document.getElementById('search').value = '';
            loadLogs(1);
        }
    </script>
</body>
</html>
