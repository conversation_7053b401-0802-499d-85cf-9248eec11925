"use strict";
module.exports = (sequelize, DataTypes) => {
  const stage_activity_log = sequelize.define(
    "stage_activity_log",
    {
      stage_log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      // User identification
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      staff_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      company_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      // Stage details
      shipment_job_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      stage_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      stage_name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      stage_action: {
        type: DataTypes.ENUM(
          'STAGE_ACCESS',
          'STAGE_ENTER',
          'STAGE_EXIT',
          'STAGE_COMPLETE',
          'STAGE_SKIP',
          'STAGE_FORCE_CHANGE',
          'STAGE_SETTINGS_MODIFY'
        ),
        allowNull: false,
      },
      // Activity details for each stage
      activity_details: {
        type: DataTypes.ENUM(
          'ADD_ITEMS_SELECTED',
          'VIEW_ITEMS_SELECTED',
          'ITEMS_ADDED_COUNT',
          'ITEM_EDITED',
          'ITEM_DUPLICATED',
          'ITEM_DELETED',
          'ASSIGN_TO_STORAGE_SELECTED',
          'UNIT_MANUAL_SELECTION',
          'UNIT_QR_SCANNED',
          'REMOVE_FROM_STORAGE_SELECTED',
          'ITEM_MANUAL_SELECTION',
          'ITEM_QR_SCANNED',
          'REMOVE_FROM_INVENTORY_SELECTED',
          'SIGNATURE_CAPTURED',
          'STAGE_ADDED',
          'STAGE_EDITED',
          'STAGE_DELETED',
          'STAGE_DEACTIVATED'
        ),
        allowNull: true,
      },
      // Inventory specific data
      items_added_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_edited_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_deleted_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_assigned_to_storage: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_removed_from_storage: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_removed_from_inventory: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      // Selection method tracking
      selection_method: {
        type: DataTypes.ENUM('MANUAL', 'QR_SCAN', 'BULK_SELECT'),
        allowNull: true,
      },
      qr_codes_scanned: {
        type: DataTypes.JSON, // Array of QR codes scanned
        allowNull: true,
      },
      // Additional context
      session_id: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      duration_seconds: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      additional_data: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      createdAt: false,
      updatedAt: false,
      freezeTableName: true,
    }
  );

  stage_activity_log.associate = function (models) {
    // associations can be defined here
    stage_activity_log.belongsTo(models.admin, {
      as: "admin_user",
      foreignKey: "admin_id",
    });

    stage_activity_log.belongsTo(models.staff, {
      as: "staff_user",
      foreignKey: "staff_id",
    });

    stage_activity_log.belongsTo(models.company, {
      as: "company",
      foreignKey: "company_id",
    });

    stage_activity_log.belongsTo(models.shipment_job, {
      as: "shipment",
      foreignKey: "shipment_job_id",
    });
  };

  return stage_activity_log;
};
