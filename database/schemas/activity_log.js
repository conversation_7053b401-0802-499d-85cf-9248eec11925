"use strict";
module.exports = (sequelize, DataTypes) => {
  const activity_log = sequelize.define(
    "activity_log",
    {
      log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      // User identification
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      staff_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      company_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      // Activity details
      activity_type: {
        type: DataTypes.ENUM(
          'LOGIN',
          'LOGOUT',
          'STAGE_ACCESS',
          'STAGE_MODIFICATION',
          'ITEM_ADD',
          'ITEM_EDIT',
          'ITEM_DELETE',
          'ITEM_DUPLICATE',
          'ITEM_ASSIGN_STORAGE',
          'ITEM_REMOVE_STORAGE',
          'ITEM_REMOVE_INVENTORY',
          'UNIT_MAPPING',
          'UNIT_MOVE',
          'QR_SCAN',
          'MANUAL_SELECTION',
          'SHIPMENT_CREATE',
          'SHIPMENT_EDIT',
          'SHIPMENT_DELETE',
          'SHIPMENT_TYPE_MANAGE',
          'ROOM_MANAGE',
          'TAG_MANAGE',
          'USER_MANAGE',
          'CUSTOMER_MANAGE',
          'SIGNATURE_CAPTURE',
          'API_CALL',
          'SYSTEM_EVENT'
        ),
        allowNull: false,
      },
      activity_category: {
        type: DataTypes.ENUM(
          'APP_INVENTORY',
          'APP_STORAGE', 
          'CMS_ADMIN',
          'API_EXTERNAL',
          'SYSTEM'
        ),
        allowNull: false,
      },
      activity_description: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      // Context information
      shipment_job_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      shipment_inventory_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      stage_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      // Technical details
      ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
      },
      user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      device_type: {
        type: DataTypes.ENUM("ios", "android", "web"),
        allowNull: true,
      },
      api_endpoint: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      http_method: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      // Data tracking
      old_values: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      new_values: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      additional_data: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      // Status and metadata
      status: {
        type: DataTypes.ENUM('SUCCESS', 'FAILED', 'WARNING'),
        defaultValue: 'SUCCESS',
      },
      error_message: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      session_id: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      createdAt: false,
      updatedAt: false,
      freezeTableName: true,
    }
  );

  activity_log.associate = function (models) {
    // associations can be defined here
    activity_log.belongsTo(models.admin, {
      as: "admin_user",
      foreignKey: "admin_id",
    });

    activity_log.belongsTo(models.staff, {
      as: "staff_user",
      foreignKey: "staff_id",
    });

    activity_log.belongsTo(models.customer, {
      as: "customer_user",
      foreignKey: "customer_id",
    });

    activity_log.belongsTo(models.company, {
      as: "company",
      foreignKey: "company_id",
    });

    activity_log.belongsTo(models.shipment_job, {
      as: "shipment",
      foreignKey: "shipment_job_id",
    });

    activity_log.belongsTo(models.shipment_inventory, {
      as: "inventory_item",
      foreignKey: "shipment_inventory_id",
    });
  };

  return activity_log;
};
