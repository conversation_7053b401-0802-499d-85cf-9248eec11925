"use strict";
module.exports = (sequelize, DataTypes) => {
  const activity_log = sequelize.define(
    "activity_log",
    {
      log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      
      // User identification
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      staff_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      company_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      
      // Action classification
      action_type: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      action_category: {
        type: DataTypes.ENUM(
          'AUTH',              // Login/Logout events
          'STAGE_ACCESS',      // Stage access tracking
          'INVENTORY_MGMT',    // Item add/edit/delete/duplicate
          'STORAGE_MGMT',      // Storage operations
          'SHIPMENT_MGMT',     // Shipment creation/management
          'USER_MGMT',         // User management
          'SYSTEM_CONFIG',     // System configuration changes
          'SIGNATURE',         // Signature capture events
          'GENERAL'            // General activities
        ),
        allowNull: false,
      },
      action_description: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      
      // Context information
      shipment_job_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      shipment_inventory_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      stage_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      stage_name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      
      // Stage access tracking
      stage_action: {
        type: DataTypes.ENUM(
          'ACCESSED',
          'ADD_ITEMS_SELECTED',
          'VIEW_ITEMS_SELECTED',
          'ASSIGN_TO_STORAGE_SELECTED',
          'REMOVE_FROM_STORAGE_SELECTED',
          'REMOVE_FROM_INVENTORY_SELECTED'
        ),
        allowNull: true,
      },
      
      // Item operations tracking
      items_added_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_edited_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_deleted_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_duplicated_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_assigned_to_storage: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_removed_from_storage: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_removed_from_inventory: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      
      // Selection method tracking
      selection_method: {
        type: DataTypes.ENUM('MANUAL', 'QR_SCAN', 'MIXED'),
        allowNull: true,
      },
      qr_codes_scanned: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      
      // Data modification tracking
      fields_modified: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      old_values: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      new_values: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      
      // Unit and storage tracking
      unit_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      storage_unit_id: {
        type: DataTypes.STRING(150),
        allowNull: true,
      },
      unit_location_from: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      unit_location_to: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      
      // Technical details
      ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
      },
      user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      device_type: {
        type: DataTypes.ENUM("ios", "android", "web"),
        allowNull: true,
      },
      session_id: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      
      // Login/Session specific fields
      login_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      logout_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      session_duration_minutes: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      
      // Additional context
      additional_data: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      
      // Status tracking
      status: {
        type: DataTypes.ENUM('SUCCESS', 'FAILED', 'WARNING'),
        defaultValue: 'SUCCESS',
      },
      error_message: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      
      // Timestamps
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      createdAt: false,
      updatedAt: false,
      freezeTableName: true,
    }
  );

  activity_log.associate = function (models) {
    // User associations
    activity_log.belongsTo(models.admin, {
      as: "admin_user",
      foreignKey: "admin_id",
    });

    activity_log.belongsTo(models.staff, {
      as: "staff_user",
      foreignKey: "staff_id",
    });

    activity_log.belongsTo(models.customer, {
      as: "customer_user",
      foreignKey: "customer_id",
    });

    activity_log.belongsTo(models.company, {
      as: "company",
      foreignKey: "company_id",
    });

    // Context associations
    activity_log.belongsTo(models.shipment_job, {
      as: "shipment",
      foreignKey: "shipment_job_id",
    });

    activity_log.belongsTo(models.shipment_inventory, {
      as: "inventory_item",
      foreignKey: "shipment_inventory_id",
    });
  };

  return activity_log;
};
