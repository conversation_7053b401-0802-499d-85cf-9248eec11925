"use strict";
module.exports = (sequelize, DataTypes) => {
  const user_session_log = sequelize.define(
    "user_session_log",
    {
      session_log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      // User identification
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      staff_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      company_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      // Session details
      session_id: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      access_token: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      action_type: {
        type: DataTypes.ENUM('LOGIN', 'LOGOUT', 'TOKEN_REFRESH', 'SESSION_EXPIRED'),
        allowNull: false,
      },
      login_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      logout_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      session_duration: {
        type: DataTypes.INTEGER, // Duration in seconds
        allowNull: true,
      },
      // Technical details
      ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
      },
      user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      device_type: {
        type: DataTypes.ENUM("ios", "android", "web"),
        allowNull: true,
      },
      device_token: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      app_version: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      // Status and metadata
      login_status: {
        type: DataTypes.ENUM('SUCCESS', 'FAILED', 'BLOCKED'),
        defaultValue: 'SUCCESS',
      },
      failure_reason: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      location_info: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      createdAt: false,
      updatedAt: false,
      freezeTableName: true,
    }
  );

  user_session_log.associate = function (models) {
    // associations can be defined here
    user_session_log.belongsTo(models.admin, {
      as: "admin_user",
      foreignKey: "admin_id",
    });

    user_session_log.belongsTo(models.staff, {
      as: "staff_user",
      foreignKey: "staff_id",
    });

    user_session_log.belongsTo(models.customer, {
      as: "customer_user",
      foreignKey: "customer_id",
    });

    user_session_log.belongsTo(models.company, {
      as: "company",
      foreignKey: "company_id",
    });
  };

  return user_session_log;
};
