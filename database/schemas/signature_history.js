"use strict";
module.exports = (sequelize, DataTypes) => {
  const signature_history = sequelize.define(
    "signature_history",
    {
      signature_history_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      
      // Context
      shipment_job_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      stage_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      stage_name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      
      // User who captured signature
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      staff_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      company_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      
      // Item counts at time of signature
      items_added_to_inventory: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_added_to_storage: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_removed_from_storage: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      items_removed_from_inventory: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      
      // Signature details
      signature_data: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      signature_type: {
        type: DataTypes.ENUM('DIGITAL', 'IMAGE', 'ELECTRONIC'),
        defaultValue: 'DIGITAL',
      },
      
      // Additional context
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
      },
      device_type: {
        type: DataTypes.ENUM("ios", "android", "web"),
        allowNull: true,
      },
      
      // Timestamps
      signature_captured_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      createdAt: false,
      updatedAt: false,
      freezeTableName: true,
    }
  );

  signature_history.associate = function (models) {
    // User associations
    signature_history.belongsTo(models.admin, {
      as: "admin_user",
      foreignKey: "admin_id",
    });

    signature_history.belongsTo(models.staff, {
      as: "staff_user",
      foreignKey: "staff_id",
    });

    signature_history.belongsTo(models.company, {
      as: "company",
      foreignKey: "company_id",
    });

    // Context associations
    signature_history.belongsTo(models.shipment_job, {
      as: "shipment",
      foreignKey: "shipment_job_id",
    });
  };

  return signature_history;
};
