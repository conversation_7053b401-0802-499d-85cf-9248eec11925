"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Create single comprehensive activity_logs table for all logging requirements
      await queryInterface.createTable("activity_logs", {
        log_id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
          comment: "Unique identifier for each log entry"
        },
        
        // USER IDENTIFICATION - Who performed the action
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: { model: "admins", key: "admin_id" },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
          comment: "CMS Admin user - Used for: CMS logging requirements (shipment creation, stage management, user management, etc.)"
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: { model: "staff", key: "staff_id" },
          onUpdate: "CASCADE", 
          onDelete: "SET NULL",
          comment: "APP Staff user - Used for: All APP logging (stage access, item operations, storage operations, login/logout)"
        },
        customer_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: { model: "customers", key: "customer_id" },
          onUpdate: "CASCADE",
          onDelete: "SET NULL", 
          comment: "Customer user - Used for: Customer portal activities and customer management logging"
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: { model: "companies", key: "company_id" },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
          comment: "Company context - Used for: Filtering logs by company, ensuring data isolation"
        },
        
        // ACTION CLASSIFICATION - What happened
        action_type: {
          type: Sequelize.STRING(100),
          allowNull: false,
          comment: "Specific action - Used for: LOGIN/LOGOUT, STAGE_ACCESS, ITEM_ADD/EDIT/DELETE/DUPLICATE, STORAGE operations, etc."
        },
        action_category: {
          type: Sequelize.ENUM(
            'AUTH',              // Login/Logout events
            'STAGE_ACCESS',      // Stage access tracking  
            'INVENTORY_MGMT',    // Item add/edit/delete/duplicate
            'STORAGE_MGMT',      // Storage operations
            'SHIPMENT_MGMT',     // Shipment creation/management
            'USER_MGMT',         // User management
            'SYSTEM_CONFIG',     // System configuration changes
            'SIGNATURE'          // Signature capture events
          ),
          allowNull: false,
          comment: "High-level categorization - Used for: Filtering logs by category in admin interface"
        },
        action_description: {
          type: Sequelize.TEXT,
          allowNull: false,
          comment: "Human-readable description - Used for: Search functionality and detailed log viewing"
        },
        
        // CONTEXT INFORMATION - Where and what context
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: { model: "shipment_jobs", key: "shipment_job_id" },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
          comment: "Shipment context - Used for: Track actions at shipment level (requirement: track actions at shipment level)"
        },
        shipment_inventory_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: { model: "shipment_inventories", key: "shipment_inventory_id" },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
          comment: "Specific inventory item - Used for: Item-level operations tracking (add, edit, delete, duplicate)"
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Stage identifier - Used for: Stage access tracking (Stages 1-4), stage modifications in CMS"
        },
        stage_name: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "Stage name - Used for: Easy identification of stages (Add Items to Inventory, Add Items to Storage, etc.)"
        },
        
        // STAGE ACCESS TRACKING - Which stage actions were performed
        stage_action: {
          type: Sequelize.ENUM(
            'ACCESSED',                        // User accessed the stage
            'ADD_ITEMS_SELECTED',             // Stage 1: Selected add items option
            'VIEW_ITEMS_SELECTED',            // All stages: Selected view items option
            'ASSIGN_TO_STORAGE_SELECTED',     // Stage 2: Selected assign to storage option
            'REMOVE_FROM_STORAGE_SELECTED',   // Stage 3: Selected remove from storage option
            'REMOVE_FROM_INVENTORY_SELECTED'  // Stage 4: Selected remove from inventory option
          ),
          allowNull: true,
          comment: "Stage-specific actions - Used for: Requirements 'capture whether user selected add/view/remove options'"
        },
        
        // ITEM OPERATIONS COUNTING - Track quantities for each operation type
        items_added_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items added - Used for: Stage 1 requirement 'record number of items added by each user', signature history"
        },
        items_edited_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items edited - Used for: Stage 1 requirement 'capture edits made to items'"
        },
        items_deleted_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items deleted - Used for: Stage 1 requirement 'track item deletion events'"
        },
        items_duplicated_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items duplicated - Used for: Stage 1 requirement 'track item duplication events'"
        },
        items_assigned_to_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items assigned to storage - Used for: Stage 2 operations, signature history 'items added to storage'"
        },
        items_removed_from_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items removed from storage - Used for: Stage 3 operations, signature history 'items removed from storage'"
        },
        items_removed_from_inventory: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items removed from inventory - Used for: Stage 4 operations, signature history 'items removed from inventory'"
        },
        
        // SELECTION METHOD TRACKING - Manual vs QR code selection
        selection_method: {
          type: Sequelize.ENUM('MANUAL', 'QR_SCAN', 'MIXED'),
          allowNull: true,
          comment: "Selection method - Used for: Requirements 'record whether user selected manually or scanned QR code' (Stages 2-4)"
        },
        qr_codes_scanned: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Array of QR codes scanned - Used for: Track specific QR codes used in operations"
        },
        
        // DATA MODIFICATION TRACKING - What changed
        fields_modified: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Modified field names - Used for: Stage 1 requirement 'fields modified such as description, notes, volume/weight'"
        },
        old_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Previous values - Used for: Track changes in CMS operations (user edits, system config changes)"
        },
        new_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "New values - Used for: Track changes in CMS operations, item additions"
        },
        
        // STORAGE UNIT TRACKING - Unit operations
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Unit identifier - Used for: Storage APP requirement 'item to unit mapping', unit assignments"
        },
        storage_unit_id: {
          type: Sequelize.STRING(150),
          allowNull: true,
          comment: "Storage unit code - Used for: Track specific storage units in operations"
        },
        unit_location_from: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "Previous location - Used for: Storage APP requirement 'unit moved from staging area to racks/vaults'"
        },
        unit_location_to: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "New location - Used for: Storage APP requirement 'unit moved from staging area to racks/vaults'"
        },
        
        // TECHNICAL DETAILS - Session and device tracking
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
          comment: "User IP address - Used for: Security tracking and troubleshooting"
        },
        user_agent: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "Browser/device info - Used for: Device identification and troubleshooting"
        },
        device_type: {
          type: Sequelize.ENUM("ios", "android", "web"),
          allowNull: true,
          comment: "Platform type - Used for: Track which platform performed the action"
        },
        session_id: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "Session identifier - Used for: Group related actions, session duration tracking"
        },
        
        // LOGIN/SESSION TRACKING - Authentication events
        login_time: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: "Login timestamp - Used for: Requirement 'log user login events with date and time'"
        },
        logout_time: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: "Logout timestamp - Used for: Track logout events and calculate session duration"
        },
        session_duration_minutes: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Session length - Used for: Track how long users were active"
        },
        
        // SIGNATURE TRACKING - Signature capture events
        signature_data: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "Signature info - Used for: Signature history requirement, track signature captures per stage"
        },
        signature_items_added_to_inventory: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items count at signature - Used for: Signature history requirement 'items added to inventory'"
        },
        signature_items_added_to_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items count at signature - Used for: Signature history requirement 'items added to storage'"
        },
        signature_items_removed_from_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items count at signature - Used for: Signature history requirement 'items removed from storage'"
        },
        signature_items_removed_from_inventory: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Items count at signature - Used for: Signature history requirement 'items removed from inventory'"
        },
        
        // ADDITIONAL CONTEXT
        additional_data: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Extra context - Used for: Store any additional information not covered by other fields"
        },
        
        // STATUS TRACKING
        status: {
          type: Sequelize.ENUM('SUCCESS', 'FAILED', 'WARNING'),
          defaultValue: 'SUCCESS',
          comment: "Operation status - Used for: Track successful vs failed operations for troubleshooting"
        },
        error_message: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "Error details - Used for: Store error information for failed operations"
        },
        
        // TIMESTAMPS
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
          comment: "Record creation time - Used for: All date/time requirements, filtering by date range"
        },
        updated_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
          comment: "Record update time - Used for: Track when log entries are modified"
        }
      });

      // Add comprehensive indexes for optimal query performance
      await queryInterface.addIndex("activity_logs", ["admin_id"], { name: "idx_activity_logs_admin_id" });
      await queryInterface.addIndex("activity_logs", ["staff_id"], { name: "idx_activity_logs_staff_id" });
      await queryInterface.addIndex("activity_logs", ["customer_id"], { name: "idx_activity_logs_customer_id" });
      await queryInterface.addIndex("activity_logs", ["company_id"], { name: "idx_activity_logs_company_id" });
      await queryInterface.addIndex("activity_logs", ["action_type"], { name: "idx_activity_logs_action_type" });
      await queryInterface.addIndex("activity_logs", ["action_category"], { name: "idx_activity_logs_action_category" });
      await queryInterface.addIndex("activity_logs", ["shipment_job_id"], { name: "idx_activity_logs_shipment_job_id" });
      await queryInterface.addIndex("activity_logs", ["stage_id"], { name: "idx_activity_logs_stage_id" });
      await queryInterface.addIndex("activity_logs", ["created_at"], { name: "idx_activity_logs_created_at" });
      await queryInterface.addIndex("activity_logs", ["session_id"], { name: "idx_activity_logs_session_id" });
      
      // Composite indexes for common filtering patterns
      await queryInterface.addIndex("activity_logs", ["company_id", "action_category", "created_at"], { name: "idx_activity_logs_company_category_date" });
      await queryInterface.addIndex("activity_logs", ["shipment_job_id", "action_type", "created_at"], { name: "idx_activity_logs_shipment_action_date" });
      await queryInterface.addIndex("activity_logs", ["staff_id", "action_category", "created_at"], { name: "idx_activity_logs_staff_category_date" });

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface) => {
    try {
      await queryInterface.dropTable("activity_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
