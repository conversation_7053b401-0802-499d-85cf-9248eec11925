"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Create single comprehensive system_logs table
      await queryInterface.createTable("system_logs", {
        log_id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        
        // User identification - who performed the action
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "admins",
            key: "admin_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "staff",
            key: "staff_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        customer_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "customers",
            key: "customer_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "companies",
            key: "company_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        
        // What happened - action details
        action_type: {
          type: Sequelize.STRING(100),
          allowNull: false,
          comment: "LOGIN, LOGOUT, STAGE_ACCESS, ITEM_ADD, ITEM_EDIT, etc."
        },
        action_category: {
          type: Sequelize.ENUM(
            'AUTH',           // Login/Logout
            'INVENTORY',      // Item operations
            'STORAGE',        // Storage operations  
            'SHIPMENT',       // Shipment management
            'ADMIN',          // CMS operations
            'SYSTEM'          // System events
          ),
          allowNull: false,
        },
        action_description: {
          type: Sequelize.TEXT,
          allowNull: false,
          comment: "Human readable description of what happened"
        },
        
        // Where it happened - context
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_jobs",
            key: "shipment_job_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_inventory_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_inventories",
            key: "shipment_inventory_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Stage where action occurred"
        },
        stage_name: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "Name of the stage for easier filtering"
        },
        
        // How it happened - technical details
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
        },
        user_agent: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        device_type: {
          type: Sequelize.ENUM("ios", "android", "web"),
          allowNull: true,
        },
        session_id: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "Session identifier for grouping related actions"
        },
        
        // API/Request details
        api_endpoint: {
          type: Sequelize.STRING(500),
          allowNull: true,
        },
        http_method: {
          type: Sequelize.STRING(10),
          allowNull: true,
        },
        
        // Data changes - what changed
        old_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Previous values before change"
        },
        new_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "New values after change"
        },
        
        // Specific tracking fields for requirements
        items_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Number of items affected"
        },
        selection_method: {
          type: Sequelize.ENUM('MANUAL', 'QR_SCAN', 'BULK'),
          allowNull: true,
          comment: "How items/units were selected"
        },
        qr_codes_scanned: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Array of QR codes scanned"
        },
        fields_modified: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "List of fields that were modified"
        },
        
        // Additional context data
        additional_data: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Any additional context data"
        },
        
        // Status and error tracking
        status: {
          type: Sequelize.ENUM('SUCCESS', 'FAILED', 'WARNING'),
          defaultValue: 'SUCCESS',
        },
        error_message: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        
        // Timing
        duration_ms: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Duration of operation in milliseconds"
        },
        
        // Standard timestamps
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
        updated_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
      });

      // Add indexes for better query performance
      await queryInterface.addIndex("system_logs", ["admin_id"], { name: "idx_system_logs_admin_id" });
      await queryInterface.addIndex("system_logs", ["staff_id"], { name: "idx_system_logs_staff_id" });
      await queryInterface.addIndex("system_logs", ["customer_id"], { name: "idx_system_logs_customer_id" });
      await queryInterface.addIndex("system_logs", ["company_id"], { name: "idx_system_logs_company_id" });
      await queryInterface.addIndex("system_logs", ["action_type"], { name: "idx_system_logs_action_type" });
      await queryInterface.addIndex("system_logs", ["action_category"], { name: "idx_system_logs_action_category" });
      await queryInterface.addIndex("system_logs", ["shipment_job_id"], { name: "idx_system_logs_shipment_job_id" });
      await queryInterface.addIndex("system_logs", ["stage_id"], { name: "idx_system_logs_stage_id" });
      await queryInterface.addIndex("system_logs", ["created_at"], { name: "idx_system_logs_created_at" });
      await queryInterface.addIndex("system_logs", ["session_id"], { name: "idx_system_logs_session_id" });
      await queryInterface.addIndex("system_logs", ["ip_address"], { name: "idx_system_logs_ip_address" });
      
      // Composite indexes for common queries
      await queryInterface.addIndex("system_logs", ["company_id", "action_category", "created_at"], { name: "idx_system_logs_company_category_date" });
      await queryInterface.addIndex("system_logs", ["shipment_job_id", "action_type", "created_at"], { name: "idx_system_logs_shipment_action_date" });

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("system_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
