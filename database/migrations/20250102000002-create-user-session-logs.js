"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Create user_session_logs table for login/logout tracking
      await queryInterface.createTable("user_session_logs", {
        session_log_id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        // User identification
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "admins",
            key: "admin_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "staff",
            key: "staff_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        customer_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "customers",
            key: "customer_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "companies",
            key: "company_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        // Session details
        session_id: {
          type: Sequelize.STRING(255),
          allowNull: false,
        },
        access_token: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        action_type: {
          type: Sequelize.ENUM('LOGIN', 'LOGOUT', 'TOKEN_REFRESH', 'SESSION_EXPIRED'),
          allowNull: false,
        },
        login_time: {
          type: Sequelize.DATE,
          allowNull: true,
        },
        logout_time: {
          type: Sequelize.DATE,
          allowNull: true,
        },
        session_duration: {
          type: Sequelize.INTEGER, // Duration in seconds
          allowNull: true,
        },
        // Technical details
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
        },
        user_agent: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        device_type: {
          type: Sequelize.ENUM("ios", "android", "web"),
          allowNull: true,
        },
        device_token: {
          type: Sequelize.STRING(255),
          allowNull: true,
        },
        app_version: {
          type: Sequelize.STRING(20),
          allowNull: true,
        },
        // Status and metadata
        login_status: {
          type: Sequelize.ENUM('SUCCESS', 'FAILED', 'BLOCKED'),
          defaultValue: 'SUCCESS',
        },
        failure_reason: {
          type: Sequelize.STRING(255),
          allowNull: true,
        },
        location_info: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
        updated_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
      });

      // Add indexes for better performance
      await queryInterface.addIndex("user_session_logs", ["admin_id"]);
      await queryInterface.addIndex("user_session_logs", ["staff_id"]);
      await queryInterface.addIndex("user_session_logs", ["customer_id"]);
      await queryInterface.addIndex("user_session_logs", ["company_id"]);
      await queryInterface.addIndex("user_session_logs", ["session_id"]);
      await queryInterface.addIndex("user_session_logs", ["action_type"]);
      await queryInterface.addIndex("user_session_logs", ["login_time"]);
      await queryInterface.addIndex("user_session_logs", ["created_at"]);
      await queryInterface.addIndex("user_session_logs", ["ip_address"]);

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("user_session_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
