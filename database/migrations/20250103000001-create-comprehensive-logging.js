"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Create comprehensive activity_logs table
      await queryInterface.createTable("activity_logs", {
        log_id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        
        // User identification - who performed the action
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "admins",
            key: "admin_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "staff",
            key: "staff_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        customer_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "customers",
            key: "customer_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "companies",
            key: "company_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        
        // Action classification
        action_type: {
          type: Sequelize.STRING(100),
          allowNull: false,
          comment: "Specific action performed (LOGIN, STAGE_ACCESS, ITEM_ADD, etc.)"
        },
        action_category: {
          type: Sequelize.ENUM(
            'AUTH',              // Login/Logout events
            'STAGE_ACCESS',      // Stage access tracking
            'INVENTORY_MGMT',    // Item add/edit/delete/duplicate
            'STORAGE_MGMT',      // Storage operations
            'SHIPMENT_MGMT',     // Shipment creation/management
            'USER_MGMT',         // User management
            'SYSTEM_CONFIG',     // System configuration changes
            'SIGNATURE',         // Signature capture events
            'GENERAL'            // General activities
          ),
          allowNull: false,
        },
        action_description: {
          type: Sequelize.TEXT,
          allowNull: false,
          comment: "Detailed description of the action performed"
        },
        
        // Context information - where and what
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_jobs",
            key: "shipment_job_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_inventory_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_inventories",
            key: "shipment_inventory_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Stage ID where action occurred"
        },
        stage_name: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "Stage name for easier identification"
        },
        
        // Specific tracking fields for requirements
        
        // Stage access tracking
        stage_action: {
          type: Sequelize.ENUM(
            'ACCESSED',          // User accessed the stage
            'ADD_ITEMS_SELECTED', // Selected add items option
            'VIEW_ITEMS_SELECTED', // Selected view items option
            'ASSIGN_TO_STORAGE_SELECTED', // Selected assign to storage
            'REMOVE_FROM_STORAGE_SELECTED', // Selected remove from storage
            'REMOVE_FROM_INVENTORY_SELECTED' // Selected remove from inventory
          ),
          allowNull: true,
        },
        
        // Item operations tracking
        items_added_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Number of items added"
        },
        items_edited_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Number of items edited"
        },
        items_deleted_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Number of items deleted"
        },
        items_duplicated_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Number of items duplicated"
        },
        items_assigned_to_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Number of items assigned to storage"
        },
        items_removed_from_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Number of items removed from storage"
        },
        items_removed_from_inventory: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          comment: "Number of items removed from inventory"
        },
        
        // Selection method tracking
        selection_method: {
          type: Sequelize.ENUM('MANUAL', 'QR_SCAN', 'MIXED'),
          allowNull: true,
          comment: "How items/units were selected"
        },
        qr_codes_scanned: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Array of QR codes that were scanned"
        },
        
        // Data modification tracking
        fields_modified: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "List of fields that were modified (description, notes, volume, weight, etc.)"
        },
        old_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Previous values before modification"
        },
        new_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "New values after modification"
        },
        
        // Unit and storage tracking
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Unit ID involved in the action"
        },
        storage_unit_id: {
          type: Sequelize.STRING(150),
          allowNull: true,
          comment: "Storage unit identifier"
        },
        unit_location_from: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "Previous location (staging area, rack, vault)"
        },
        unit_location_to: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: "New location (staging area, rack, vault)"
        },
        
        // Technical details
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
        },
        user_agent: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        device_type: {
          type: Sequelize.ENUM("ios", "android", "web"),
          allowNull: true,
        },
        session_id: {
          type: Sequelize.STRING(255),
          allowNull: true,
        },
        
        // Login/Session specific fields
        login_time: {
          type: Sequelize.DATE,
          allowNull: true,
        },
        logout_time: {
          type: Sequelize.DATE,
          allowNull: true,
        },
        session_duration_minutes: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Session duration in minutes"
        },
        
        // Additional context
        additional_data: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Any additional context data"
        },
        
        // Status tracking
        status: {
          type: Sequelize.ENUM('SUCCESS', 'FAILED', 'WARNING'),
          defaultValue: 'SUCCESS',
        },
        error_message: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        
        // Timestamps
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
        updated_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
      });

      // Add comprehensive indexes for optimal query performance
      await queryInterface.addIndex("activity_logs", ["admin_id"], { name: "idx_activity_logs_admin_id" });
      await queryInterface.addIndex("activity_logs", ["staff_id"], { name: "idx_activity_logs_staff_id" });
      await queryInterface.addIndex("activity_logs", ["customer_id"], { name: "idx_activity_logs_customer_id" });
      await queryInterface.addIndex("activity_logs", ["company_id"], { name: "idx_activity_logs_company_id" });
      await queryInterface.addIndex("activity_logs", ["action_type"], { name: "idx_activity_logs_action_type" });
      await queryInterface.addIndex("activity_logs", ["action_category"], { name: "idx_activity_logs_action_category" });
      await queryInterface.addIndex("activity_logs", ["shipment_job_id"], { name: "idx_activity_logs_shipment_job_id" });
      await queryInterface.addIndex("activity_logs", ["stage_id"], { name: "idx_activity_logs_stage_id" });
      await queryInterface.addIndex("activity_logs", ["created_at"], { name: "idx_activity_logs_created_at" });
      await queryInterface.addIndex("activity_logs", ["session_id"], { name: "idx_activity_logs_session_id" });
      
      // Composite indexes for common query patterns
      await queryInterface.addIndex("activity_logs", ["company_id", "action_category", "created_at"], { name: "idx_activity_logs_company_category_date" });
      await queryInterface.addIndex("activity_logs", ["shipment_job_id", "action_type", "created_at"], { name: "idx_activity_logs_shipment_action_date" });
      await queryInterface.addIndex("activity_logs", ["staff_id", "action_category", "created_at"], { name: "idx_activity_logs_staff_category_date" });
      await queryInterface.addIndex("activity_logs", ["action_type", "stage_id", "created_at"], { name: "idx_activity_logs_action_stage_date" });

      // Create signature_history table for stage signature tracking
      await queryInterface.createTable("signature_history", {
        signature_history_id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },

        // Context
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: "shipment_jobs",
            key: "shipment_job_id",
          },
          onUpdate: "CASCADE",
          onDelete: "CASCADE",
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        stage_name: {
          type: Sequelize.STRING(255),
          allowNull: true,
        },

        // User who captured signature
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "admins",
            key: "admin_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "staff",
            key: "staff_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "companies",
            key: "company_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },

        // Item counts at time of signature
        items_added_to_inventory: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
        items_added_to_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
        items_removed_from_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
        items_removed_from_inventory: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },

        // Signature details
        signature_data: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "Base64 encoded signature image or signature file path"
        },
        signature_type: {
          type: Sequelize.ENUM('DIGITAL', 'IMAGE', 'ELECTRONIC'),
          defaultValue: 'DIGITAL',
        },

        // Additional context
        notes: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
        },
        device_type: {
          type: Sequelize.ENUM("ios", "android", "web"),
          allowNull: true,
        },

        // Timestamps
        signature_captured_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
        updated_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
      });

      // Add indexes for signature_history
      await queryInterface.addIndex("signature_history", ["shipment_job_id"], { name: "idx_signature_history_shipment_job_id" });
      await queryInterface.addIndex("signature_history", ["stage_id"], { name: "idx_signature_history_stage_id" });
      await queryInterface.addIndex("signature_history", ["staff_id"], { name: "idx_signature_history_staff_id" });
      await queryInterface.addIndex("signature_history", ["admin_id"], { name: "idx_signature_history_admin_id" });
      await queryInterface.addIndex("signature_history", ["signature_captured_at"], { name: "idx_signature_history_captured_at" });
      await queryInterface.addIndex("signature_history", ["shipment_job_id", "stage_id"], { name: "idx_signature_history_shipment_stage" });

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface) => {
    try {
      await queryInterface.dropTable("signature_history");
      await queryInterface.dropTable("activity_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
