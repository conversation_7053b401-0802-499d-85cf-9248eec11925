"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Create stage_activity_logs table for detailed stage tracking
      await queryInterface.createTable("stage_activity_logs", {
        stage_log_id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        // User identification
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "admins",
            key: "admin_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "staff",
            key: "staff_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "companies",
            key: "company_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        // Stage details
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: "shipment_jobs",
            key: "shipment_job_id",
          },
          onUpdate: "CASCADE",
          onDelete: "CASCADE",
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        stage_name: {
          type: Sequelize.STRING(255),
          allowNull: true,
        },
        stage_action: {
          type: Sequelize.ENUM(
            'STAGE_ACCESS',
            'STAGE_ENTER',
            'STAGE_EXIT',
            'STAGE_COMPLETE',
            'STAGE_SKIP',
            'STAGE_FORCE_CHANGE',
            'STAGE_SETTINGS_MODIFY'
          ),
          allowNull: false,
        },
        // Activity details for each stage
        activity_details: {
          type: Sequelize.ENUM(
            'ADD_ITEMS_SELECTED',
            'VIEW_ITEMS_SELECTED',
            'ITEMS_ADDED_COUNT',
            'ITEM_EDITED',
            'ITEM_DUPLICATED',
            'ITEM_DELETED',
            'ASSIGN_TO_STORAGE_SELECTED',
            'UNIT_MANUAL_SELECTION',
            'UNIT_QR_SCANNED',
            'REMOVE_FROM_STORAGE_SELECTED',
            'ITEM_MANUAL_SELECTION',
            'ITEM_QR_SCANNED',
            'REMOVE_FROM_INVENTORY_SELECTED',
            'SIGNATURE_CAPTURED',
            'STAGE_ADDED',
            'STAGE_EDITED',
            'STAGE_DELETED',
            'STAGE_DEACTIVATED'
          ),
          allowNull: true,
        },
        // Inventory specific data
        items_added_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
        items_edited_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
        items_deleted_count: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
        items_assigned_to_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
        items_removed_from_storage: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
        items_removed_from_inventory: {
          type: Sequelize.INTEGER,
          defaultValue: 0,
        },
        // Selection method tracking
        selection_method: {
          type: Sequelize.ENUM('MANUAL', 'QR_SCAN', 'BULK_SELECT'),
          allowNull: true,
        },
        qr_codes_scanned: {
          type: Sequelize.JSON, // Array of QR codes scanned
          allowNull: true,
        },
        // Additional context
        session_id: {
          type: Sequelize.STRING(255),
          allowNull: true,
        },
        duration_seconds: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        additional_data: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
        updated_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
      });

      // Add indexes for better performance
      await queryInterface.addIndex("stage_activity_logs", ["admin_id"]);
      await queryInterface.addIndex("stage_activity_logs", ["staff_id"]);
      await queryInterface.addIndex("stage_activity_logs", ["company_id"]);
      await queryInterface.addIndex("stage_activity_logs", ["shipment_job_id"]);
      await queryInterface.addIndex("stage_activity_logs", ["stage_id"]);
      await queryInterface.addIndex("stage_activity_logs", ["stage_action"]);
      await queryInterface.addIndex("stage_activity_logs", ["activity_details"]);
      await queryInterface.addIndex("stage_activity_logs", ["created_at"]);
      await queryInterface.addIndex("stage_activity_logs", ["session_id"]);

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("stage_activity_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
