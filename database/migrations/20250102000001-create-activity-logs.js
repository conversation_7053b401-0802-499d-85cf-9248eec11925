"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Create activity_logs table for comprehensive logging
      await queryInterface.createTable("activity_logs", {
        log_id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        // User identification
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "admins",
            key: "admin_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "staff",
            key: "staff_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        customer_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "customers",
            key: "customer_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "companies",
            key: "company_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        // Activity details
        activity_type: {
          type: Sequelize.ENUM(
            'LOGIN',
            'LOGOUT',
            'STAGE_ACCESS',
            'STAGE_MODIFICATION',
            'ITEM_ADD',
            'ITEM_EDIT',
            'ITEM_DELETE',
            'ITEM_DUPLICATE',
            'ITEM_ASSIGN_STORAGE',
            'ITEM_REMOVE_STORAGE',
            'ITEM_REMOVE_INVENTORY',
            'UNIT_MAPPING',
            'UNIT_MOVE',
            'QR_SCAN',
            'MANUAL_SELECTION',
            'SHIPMENT_CREATE',
            'SHIPMENT_EDIT',
            'SHIPMENT_DELETE',
            'SHIPMENT_TYPE_MANAGE',
            'ROOM_MANAGE',
            'TAG_MANAGE',
            'USER_MANAGE',
            'CUSTOMER_MANAGE',
            'SIGNATURE_CAPTURE',
            'API_CALL',
            'SYSTEM_EVENT'
          ),
          allowNull: false,
        },
        activity_category: {
          type: Sequelize.ENUM(
            'APP_INVENTORY',
            'APP_STORAGE', 
            'CMS_ADMIN',
            'API_EXTERNAL',
            'SYSTEM'
          ),
          allowNull: false,
        },
        activity_description: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        // Context information
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_jobs",
            key: "shipment_job_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        shipment_inventory_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: "shipment_inventories",
            key: "shipment_inventory_id",
          },
          onUpdate: "CASCADE",
          onDelete: "SET NULL",
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        // Technical details
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
        },
        user_agent: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        device_type: {
          type: Sequelize.ENUM("ios", "android", "web"),
          allowNull: true,
        },
        api_endpoint: {
          type: Sequelize.STRING(255),
          allowNull: true,
        },
        http_method: {
          type: Sequelize.STRING(10),
          allowNull: true,
        },
        // Data tracking
        old_values: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        new_values: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        additional_data: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        // Status and metadata
        status: {
          type: Sequelize.ENUM('SUCCESS', 'FAILED', 'WARNING'),
          defaultValue: 'SUCCESS',
        },
        error_message: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        session_id: {
          type: Sequelize.STRING(255),
          allowNull: true,
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
        updated_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
      });

      // Add indexes for better performance
      await queryInterface.addIndex("activity_logs", ["admin_id"]);
      await queryInterface.addIndex("activity_logs", ["staff_id"]);
      await queryInterface.addIndex("activity_logs", ["customer_id"]);
      await queryInterface.addIndex("activity_logs", ["company_id"]);
      await queryInterface.addIndex("activity_logs", ["activity_type"]);
      await queryInterface.addIndex("activity_logs", ["activity_category"]);
      await queryInterface.addIndex("activity_logs", ["shipment_job_id"]);
      await queryInterface.addIndex("activity_logs", ["created_at"]);
      await queryInterface.addIndex("activity_logs", ["session_id"]);

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("activity_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
