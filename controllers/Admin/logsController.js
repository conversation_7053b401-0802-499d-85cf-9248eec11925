const { system_log, admin, staff, customer, company, shipment_job } = require("../../database/schemas");
const { Op } = require("sequelize");
const moment = require("moment");

/**
 * Get logs with filtering and pagination
 */
exports.getLogs = async (request, response) => {
  try {
    const {
      page = 1,
      limit = 50,
      action_type,
      action_category,
      user_type, // 'admin', 'staff', 'customer'
      user_id,
      company_id,
      shipment_job_id,
      date_from,
      date_to,
      search,
      status
    } = request.query;

    // Build where conditions
    const whereConditions = {};

    // Filter by action type
    if (action_type) {
      whereConditions.action_type = action_type;
    }

    // Filter by action category
    if (action_category) {
      whereConditions.action_category = action_category;
    }

    // Filter by user type and ID
    if (user_type && user_id) {
      switch (user_type) {
        case 'admin':
          whereConditions.admin_id = user_id;
          break;
        case 'staff':
          whereConditions.staff_id = user_id;
          break;
        case 'customer':
          whereConditions.customer_id = user_id;
          break;
      }
    }

    // Filter by company
    if (company_id) {
      whereConditions.company_id = company_id;
    }

    // Filter by shipment
    if (shipment_job_id) {
      whereConditions.shipment_job_id = shipment_job_id;
    }

    // Filter by status
    if (status) {
      whereConditions.status = status;
    }

    // Filter by date range
    if (date_from || date_to) {
      whereConditions.created_at = {};
      if (date_from) {
        whereConditions.created_at[Op.gte] = moment(date_from).startOf('day').toDate();
      }
      if (date_to) {
        whereConditions.created_at[Op.lte] = moment(date_to).endOf('day').toDate();
      }
    }

    // Search in description
    if (search) {
      whereConditions.action_description = {
        [Op.like]: `%${search}%`
      };
    }

    // Calculate offset
    const offset = (page - 1) * limit;

    // Get logs with associations
    const logs = await system_log.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: admin,
          as: 'admin_user',
          attributes: ['admin_id', 'name', 'email'],
          required: false
        },
        {
          model: staff,
          as: 'staff_user',
          attributes: ['staff_id', 'name', 'email'],
          required: false
        },
        {
          model: customer,
          as: 'customer_user',
          attributes: ['customer_id', 'name', 'email'],
          required: false
        },
        {
          model: company,
          as: 'company',
          attributes: ['company_id', 'company_name'],
          required: false
        },
        {
          model: shipment_job,
          as: 'shipment',
          attributes: ['shipment_job_id', 'job_name', 'job_number'],
          required: false
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Calculate pagination info
    const totalPages = Math.ceil(logs.count / limit);

    response.status(200).json({
      status: 1,
      message: "Logs retrieved successfully",
      data: {
        logs: logs.rows,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_records: logs.count,
          per_page: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching logs:', error);
    response.status(500).json({
      status: 0,
      message: "Error fetching logs",
      data: { error: error.message }
    });
  }
};

/**
 * Get log statistics/summary
 */
exports.getLogStats = async (request, response) => {
  try {
    const { company_id, date_from, date_to } = request.query;

    const whereConditions = {};
    
    if (company_id) {
      whereConditions.company_id = company_id;
    }

    if (date_from || date_to) {
      whereConditions.created_at = {};
      if (date_from) {
        whereConditions.created_at[Op.gte] = moment(date_from).startOf('day').toDate();
      }
      if (date_to) {
        whereConditions.created_at[Op.lte] = moment(date_to).endOf('day').toDate();
      }
    }

    // Get counts by category
    const categoryStats = await system_log.findAll({
      where: whereConditions,
      attributes: [
        'action_category',
        [system_log.sequelize.fn('COUNT', system_log.sequelize.col('log_id')), 'count']
      ],
      group: ['action_category']
    });

    // Get counts by action type
    const actionTypeStats = await system_log.findAll({
      where: whereConditions,
      attributes: [
        'action_type',
        [system_log.sequelize.fn('COUNT', system_log.sequelize.col('log_id')), 'count']
      ],
      group: ['action_type'],
      order: [[system_log.sequelize.fn('COUNT', system_log.sequelize.col('log_id')), 'DESC']],
      limit: 10
    });

    // Get daily activity for the last 7 days
    const sevenDaysAgo = moment().subtract(7, 'days').startOf('day').toDate();
    const dailyActivity = await system_log.findAll({
      where: {
        ...whereConditions,
        created_at: {
          [Op.gte]: sevenDaysAgo
        }
      },
      attributes: [
        [system_log.sequelize.fn('DATE', system_log.sequelize.col('created_at')), 'date'],
        [system_log.sequelize.fn('COUNT', system_log.sequelize.col('log_id')), 'count']
      ],
      group: [system_log.sequelize.fn('DATE', system_log.sequelize.col('created_at'))],
      order: [[system_log.sequelize.fn('DATE', system_log.sequelize.col('created_at')), 'ASC']]
    });

    // Get error count
    const errorCount = await system_log.count({
      where: {
        ...whereConditions,
        status: 'FAILED'
      }
    });

    response.status(200).json({
      status: 1,
      message: "Log statistics retrieved successfully",
      data: {
        category_stats: categoryStats,
        action_type_stats: actionTypeStats,
        daily_activity: dailyActivity,
        error_count: errorCount
      }
    });

  } catch (error) {
    console.error('Error fetching log stats:', error);
    response.status(500).json({
      status: 0,
      message: "Error fetching log statistics",
      data: { error: error.message }
    });
  }
};

/**
 * Get detailed log by ID
 */
exports.getLogById = async (request, response) => {
  try {
    const { log_id } = request.params;

    const log = await system_log.findByPk(log_id, {
      include: [
        {
          model: admin,
          as: 'admin_user',
          attributes: ['admin_id', 'name', 'email'],
          required: false
        },
        {
          model: staff,
          as: 'staff_user',
          attributes: ['staff_id', 'name', 'email'],
          required: false
        },
        {
          model: customer,
          as: 'customer_user',
          attributes: ['customer_id', 'name', 'email'],
          required: false
        },
        {
          model: company,
          as: 'company',
          attributes: ['company_id', 'company_name'],
          required: false
        },
        {
          model: shipment_job,
          as: 'shipment',
          attributes: ['shipment_job_id', 'job_name', 'job_number'],
          required: false
        }
      ]
    });

    if (!log) {
      return response.status(404).json({
        status: 0,
        message: "Log not found",
        data: {}
      });
    }

    response.status(200).json({
      status: 1,
      message: "Log retrieved successfully",
      data: { log }
    });

  } catch (error) {
    console.error('Error fetching log:', error);
    response.status(500).json({
      status: 0,
      message: "Error fetching log",
      data: { error: error.message }
    });
  }
};

/**
 * Export logs to CSV
 */
exports.exportLogs = async (request, response) => {
  try {
    // This would implement CSV export functionality
    // For now, just return a success message
    response.status(200).json({
      status: 1,
      message: "Export functionality to be implemented",
      data: {}
    });
  } catch (error) {
    console.error('Error exporting logs:', error);
    response.status(500).json({
      status: 0,
      message: "Error exporting logs",
      data: { error: error.message }
    });
  }
};
