const express = require("express");
const cors = require("cors");
const morgan = require("morgan");
const bodyParser = require("body-parser");
const routes = require("./routes/index");
const swaggerJsdoc = require('./swagger.json');
const swaggerUi = require("swagger-ui-express");
const timeout = require('connect-timeout');
const path = require("path");
const { apiLoggingMiddleware } = require("./middlewares/loggingMiddleware");

require("dotenv").config();

const app = express();

app.use(timeout('3600000ms', { "respond": true }))
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerJsdoc))
app.use(express.json({ limit: "100mb" }));
app.use(express.urlencoded({ extended: false, limit: "100mb" }));

const corsOptions = {
	// origin: "*",
	origin: true,
	methods: "PUT, POST, PATCH, DELETE, GET",
	allowedHeaders:
		"Origin,Access-Control-Allow-Origin,X-Requested-With, Content-Type, Accept, Authorization, x_api_key, user_id, token,device_token,access_token, admin_id,company_id, staff_id,version,device_type, user_type",
	optionsSuccessStatus: 200,
};

app.use(cors(corsOptions));

//Configure S3
const AWS = require("aws-sdk");
AWS.config.update({
	accessKeyId: process.env.AWS_ACCESS_KEY_ID,
	secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
});

// Logging middleware
app.use(morgan("dev"));
app.use(apiLoggingMiddleware);

app.use("/", routes);
app.use("/temp", express.static(path.join(__dirname, "temp")));

app.listen(process.env.APP_PORT, () => {
	console.log("running on port " + process.env.APP_PORT);
});

app.use(haltOnTimedout);

function haltOnTimedout(err, req, res, next) {
	if (req.timedout === true) {
		res.status(503).json({ status: 0, message: "Service unavailable. Please try again!", data: {} })
	} else {
		next();
	}
};

module.exports = app;
